/**
  ******************************************************************************
  * @file    Project/STM32F10x_StdPeriph_Template/stm32f10x_it.c 
  * <AUTHOR> Application Team
  * @version V3.5.0
  * @date    08-April-2011
  * @brief   Main Interrupt Service Routines.
  *          This file provides template for all exceptions handler and 
  *          peripherals interrupt service routine.
  ******************************************************************************
  * @attention
  *
  * THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
  * WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO SAVE
  * TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR ANY
  * DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY CLAIMS ARISING
  * FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOMERS OF THE
  * CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR PRODUCTS.
  *
  * <h2><center>&copy; COPYRIGHT 2011 STMicroelectronics</center></h2>
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stm32f10x_it.h"
#include "stm32f10x_usart.h"
#include <stdlib.h>  // for atoi, atof functions

/** @addtogroup STM32F10x_StdPeriph_Template
  * @{
  */

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/******************************************************************************/
/*            Cortex-M3 Processor Exceptions Handlers                         */
/******************************************************************************/

/**
  * @brief  This function handles NMI exception.
  * @param  None
  * @retval None
  */
void NMI_Handler(void)
{
}

/**
  * @brief  This function handles Hard Fault exception.
  * @param  None
  * @retval None
  */
void HardFault_Handler(void)
{
  /* Go to infinite loop when Hard Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Memory Manage exception.
  * @param  None
  * @retval None
  */
void MemManage_Handler(void)
{
  /* Go to infinite loop when Memory Manage exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Bus Fault exception.
  * @param  None
  * @retval None
  */
void BusFault_Handler(void)
{
  /* Go to infinite loop when Bus Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Usage Fault exception.
  * @param  None
  * @retval None
  */
void UsageFault_Handler(void)
{
  /* Go to infinite loop when Usage Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles SVCall exception.
  * @param  None
  * @retval None
  */
void SVC_Handler(void)
{
}

/**
  * @brief  This function handles Debug Monitor exception.
  * @param  None
  * @retval None
  */
void DebugMon_Handler(void)
{
}

/**
  * @brief  This function handles PendSVC exception.
  * @param  None
  * @retval None
  */
void PendSV_Handler(void)
{
}

/**
  * @brief  This function handles SysTick Handler.
  * @param  None
  * @retval None
  */
void SysTick_Handler(void)
{
// 	TimingDelay_Decrement();
}

/******************************************************************************/
/*                 STM32F10x Peripherals Interrupt Handlers                   */
/*  Add here the Interrupt Handler for the used peripheral(s) (PPP), for the  */
/*  available peripheral interrupt handler's name please refer to the startup */
/*  file (startup_stm32f10x_xx.s).                                            */
/******************************************************************************/

/**
  * @brief  This function handles PPP interrupt request.
  * @param  None
  * @retval None
  */
/*void PPP_IRQHandler(void)
{
}*/

// 串口接收相关变量声明
extern s32 uart_fre1;
extern float uart_q;
extern u8 uart_mode;
extern u8 uart_data_ready;

// 串口接收缓冲区和状态变量 - 二进制数据包格式
#define DATA_PACKET_SIZE 9  // 4字节float + 4字节频率 + 1字节模式
static u8 uart_rx_buffer[DATA_PACKET_SIZE];
static u8 uart_rx_index = 0;

/**
  * @brief  This function handles USART1 interrupt request.
  * @param  None
  * @retval None
  * @note   接收9字节数据包：4字节float(Q值) + 4字节频率 + 1字节模式
  */
void USART1_IRQHandler(void)
{
	u8 received_data;

	if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET)
	{
		received_data = USART_ReceiveData(USART1);

		// 接收数据到缓冲区
		if(uart_rx_index < DATA_PACKET_SIZE)
		{
			uart_rx_buffer[uart_rx_index++] = received_data;

			// 如果接收完整个数据包（9字节）
			if(uart_rx_index >= DATA_PACKET_SIZE)
			{
				// 解析数据包
				// 前4字节：float类型Q值
				uart_q = *((float*)&uart_rx_buffer[0]);

				// 中间4字节：32位频率值
				uart_fre1 = *((u32*)&uart_rx_buffer[4]);

				// 最后1字节：模式值
				uart_mode = uart_rx_buffer[8];

				// 设置数据就绪标志
				uart_data_ready = 1;

				// 重置接收索引，准备接收下一个数据包
				uart_rx_index = 0;
			}
		}
		else
		{
			// 缓冲区溢出，重置
			uart_rx_index = 0;
		}

		USART_ClearITPendingBit(USART1, USART_IT_RXNE);
	}
}

/**
  * @}
  */ 


/******************* (C) COPYRIGHT 2011 STMicroelectronics *****END OF FILE****/
