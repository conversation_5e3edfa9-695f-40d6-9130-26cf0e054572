Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(.text) refers to peripheralinit.o(.text) for PeripheralInit
    main.o(.text) refers to delay.o(.text) for Delay_1ms
    main.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    main.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    main.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    main.o(.text) refers to dfixui.o(.text) for __aeabi_d2uiz
    main.o(.text) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(.text) refers to pwmoutput.o(.text) for TIM2_PWMOutput_Init
    main.o(.text) refers to max262.o(.text) for Filter1
    main.o(.text) refers to main.o(.data) for uart_data_ready
    main.o(.text) refers to pwmoutput.o(.data) for divf
    peripheralinit.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    peripheralinit.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    peripheralinit.o(.text) refers to misc.o(.text) for NVIC_Init
    peripheralinit.o(.text) refers to charlcd.o(.text) for LCM_Init
    peripheralinit.o(.text) refers to key.o(.text) for Key_Init
    peripheralinit.o(.text) refers to pwmoutput.o(.text) for TIM2_PWMOutput_Init
    max262.o(.text) refers to fdiv.o(.text) for __aeabi_fdiv
    max262.o(.text) refers to fadd.o(.text) for __aeabi_frsub
    max262.o(.text) refers to ffixui.o(.text) for __aeabi_f2uiz
    max262.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    max262.o(.text) refers to delay.o(.text) for Delay_ns
    max262.o(.text) refers to main.o(.data) for Fn
    pwmoutput.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    pwmoutput.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    pwmoutput.o(.text) refers to stm32f10x_tim.o(.text) for TIM_TimeBaseInit
    pwmoutput.o(.text) refers to pwmoutput.o(.data) for counterf
    key.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    key.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to delay.o(.text) for Delay_1ms
    charlcd.o(.text) refers to delay.o(.text) for Delay_ns
    charlcd.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    charlcd.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    charlcd.o(.text) refers to ldiv.o(.text) for __aeabi_ldivmod
    charlcd.o(.text) refers to charlcd.o(.data) for lcdbuff
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_tim.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing stm32f10x_fsmc.o(.text), (1548 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).

3 unused section(s) (total 2092 bytes) removed from the image.
