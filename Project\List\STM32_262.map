Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(.text) refers to peripheralinit.o(.text) for PeripheralInit
    main.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    main.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    main.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    main.o(.text) refers to dfixui.o(.text) for __aeabi_d2uiz
    main.o(.text) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(.text) refers to pwmoutput.o(.text) for TIM2_PWMOutput_Init
    main.o(.text) refers to max262.o(.text) for Filter1
    main.o(.text) refers to delay.o(.text) for Delay_1ms
    main.o(.text) refers to main.o(.data) for Fn
    main.o(.text) refers to pwmoutput.o(.data) for divf
    peripheralinit.o(.text) refers to charlcd.o(.text) for LCM_Init
    peripheralinit.o(.text) refers to key.o(.text) for Key_Init
    peripheralinit.o(.text) refers to pwmoutput.o(.text) for TIM2_PWMOutput_Init
    peripheralinit.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    peripheralinit.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    max262.o(.text) refers to fdiv.o(.text) for __aeabi_fdiv
    max262.o(.text) refers to fadd.o(.text) for __aeabi_frsub
    max262.o(.text) refers to ffixui.o(.text) for __aeabi_f2uiz
    max262.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    max262.o(.text) refers to delay.o(.text) for Delay_ns
    max262.o(.text) refers to main.o(.data) for Fn
    pwmoutput.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    pwmoutput.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    pwmoutput.o(.text) refers to stm32f10x_tim.o(.text) for TIM_TimeBaseInit
    pwmoutput.o(.text) refers to pwmoutput.o(.data) for counterf
    key.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    key.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to delay.o(.text) for Delay_1ms
    charlcd.o(.text) refers to delay.o(.text) for Delay_ns
    charlcd.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    charlcd.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    charlcd.o(.text) refers to ldiv.o(.text) for __aeabi_ldivmod
    charlcd.o(.text) refers to charlcd.o(.data) for lcdbuff
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_tim.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing misc.o(.text), (220 bytes).
    Removing stm32f10x_fsmc.o(.text), (1548 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).

4 unused section(s) (total 2312 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\CMSIS\CoreSupport\core_cm3.c          0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s 0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.c 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c 0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\User\CharLCD.c                        0x00000000   Number         0  charlcd.o ABSOLUTE
    ..\User\Delay.c                          0x00000000   Number         0  delay.o ABSOLUTE
    ..\User\MAX262.c                         0x00000000   Number         0  max262.o ABSOLUTE
    ..\User\PWMOutput.c                      0x00000000   Number         0  pwmoutput.o ABSOLUTE
    ..\User\PeripheralInit.c                 0x00000000   Number         0  peripheralinit.o ABSOLUTE
    ..\User\key.c                            0x00000000   Number         0  key.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\\CMSIS\\CoreSupport\\core_cm3.c       0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section        0  main.o(.text)
    .text                                    0x08000294   Section        0  delay.o(.text)
    .text                                    0x080003bc   Section        0  peripheralinit.o(.text)
    .text                                    0x0800040c   Section        0  max262.o(.text)
    .text                                    0x08000710   Section        0  pwmoutput.o(.text)
    .text                                    0x080007cc   Section        0  key.o(.text)
    .text                                    0x0800089c   Section        0  charlcd.o(.text)
    .text                                    0x08000ef4   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x08001250   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x080015f4   Section        0  stm32f10x_tim.o(.text)
    TI4_Config                               0x08001a9b   Thumb Code   130  stm32f10x_tim.o(.text)
    TI3_Config                               0x08001b2f   Thumb Code   122  stm32f10x_tim.o(.text)
    TI2_Config                               0x08001bc3   Thumb Code   130  stm32f10x_tim.o(.text)
    TI1_Config                               0x08001c57   Thumb Code   108  stm32f10x_tim.o(.text)
    .text                                    0x08002410   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x08002411   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x080024e7   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x080025f0   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08002614   Section        0  ldiv.o(.text)
    .text                                    0x08002676   Section        0  fadd.o(.text)
    .text                                    0x08002726   Section        0  fdiv.o(.text)
    .text                                    0x080027a2   Section        0  dadd.o(.text)
    .text                                    0x080028f0   Section        0  dmul.o(.text)
    .text                                    0x080029d4   Section        0  ddiv.o(.text)
    .text                                    0x08002ab2   Section        0  dflti.o(.text)
    .text                                    0x08002ad4   Section        0  dfltui.o(.text)
    .text                                    0x08002aee   Section        0  ffixui.o(.text)
    .text                                    0x08002b16   Section        0  dfixui.o(.text)
    .text                                    0x08002b48   Section        0  uldiv.o(.text)
    .text                                    0x08002baa   Section        0  llshl.o(.text)
    .text                                    0x08002bc8   Section        0  llushr.o(.text)
    .text                                    0x08002be8   Section        0  llsshr.o(.text)
    .text                                    0x08002c0c   Section        0  iusefp.o(.text)
    .text                                    0x08002c0c   Section        0  fepilogue.o(.text)
    .text                                    0x08002c7a   Section        0  depilogue.o(.text)
    .text                                    0x08002d34   Section       36  init.o(.text)
    i.__scatterload_copy                     0x08002d58   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08002d66   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08002d68   Section       14  handlers.o(i.__scatterload_zeroinit)
    .data                                    0x20000000   Section        1  main.o(.data)
    .data                                    0x20000004   Section        8  pwmoutput.o(.data)
    .data                                    0x2000000c   Section      129  charlcd.o(.data)
    .data                                    0x2000008d   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000008d   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x2000009d   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x200000a4   Section       20  system_stm32f10x.o(.data)
    STACK                                    0x200000b8   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    main                                     0x08000145   Thumb Code   274  main.o(.text)
    TimingDelay_Decrement                    0x08000295   Thumb Code     2  delay.o(.text)
    Delay_ns                                 0x08000297   Thumb Code    12  delay.o(.text)
    Delay_1us                                0x080002a3   Thumb Code    26  delay.o(.text)
    Delay_2us                                0x080002bd   Thumb Code    26  delay.o(.text)
    Delay_10us                               0x080002d7   Thumb Code    36  delay.o(.text)
    Delay_250us                              0x080002fb   Thumb Code    36  delay.o(.text)
    Delay_882us                              0x0800031f   Thumb Code    26  delay.o(.text)
    Delay_1ms                                0x08000339   Thumb Code    36  delay.o(.text)
    Delay_5ms                                0x0800035d   Thumb Code    38  delay.o(.text)
    Delay_50ms                               0x08000383   Thumb Code    40  delay.o(.text)
    Delay                                    0x080003ab   Thumb Code    18  delay.o(.text)
    PeripheralInit                           0x080003bd   Thumb Code    16  peripheralinit.o(.text)
    MAX262_GPIO_Init                         0x080003cd   Thumb Code    56  peripheralinit.o(.text)
    Qn                                       0x0800040d   Thumb Code    40  max262.o(.text)
    Filter1                                  0x08000435   Thumb Code   348  max262.o(.text)
    Filter2                                  0x08000591   Thumb Code   372  max262.o(.text)
    TIM2_GPIO_Init                           0x08000711   Thumb Code    38  pwmoutput.o(.text)
    TIM2_Mode_Init                           0x08000737   Thumb Code   120  pwmoutput.o(.text)
    TIM2_PWMOutput_Init                      0x080007af   Thumb Code    12  pwmoutput.o(.text)
    Key_Init                                 0x080007cd   Thumb Code    38  key.o(.text)
    Key_Scan                                 0x080007f3   Thumb Code   164  key.o(.text)
    Wr_CodeData                              0x0800089d   Thumb Code   166  charlcd.o(.text)
    WrCLcdD                                  0x08000943   Thumb Code    38  charlcd.o(.text)
    WrCLcdC                                  0x08000969   Thumb Code    38  charlcd.o(.text)
    CG_Write                                 0x0800098f   Thumb Code    30  charlcd.o(.text)
    GPIO_LCM_Configuration                   0x080009ad   Thumb Code    64  charlcd.o(.text)
    LCM_Init                                 0x080009ed   Thumb Code    60  charlcd.o(.text)
    WriteString                              0x08000a29   Thumb Code    52  charlcd.o(.text)
    WrCLcd_char_num                          0x08000a5d   Thumb Code   174  charlcd.o(.text)
    WrCLcd_int_num                           0x08000b0b   Thumb Code   226  charlcd.o(.text)
    WrCLcd_long_num                          0x08000bed   Thumb Code   454  charlcd.o(.text)
    Wr_In1                                   0x08000db3   Thumb Code    36  charlcd.o(.text)
    Wr_In2                                   0x08000dd7   Thumb Code    36  charlcd.o(.text)
    CL_Enter                                 0x08000dfb   Thumb Code    44  charlcd.o(.text)
    CR_Enter                                 0x08000e27   Thumb Code    42  charlcd.o(.text)
    L_Enter                                  0x08000e51   Thumb Code    42  charlcd.o(.text)
    R_Enter                                  0x08000e7b   Thumb Code    42  charlcd.o(.text)
    CGWrite                                  0x08000ea5   Thumb Code    62  charlcd.o(.text)
    GPIO_DeInit                              0x08000ef5   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08000fa1   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x08000fb5   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x080010cb   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x080010db   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x080010ed   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x080010f5   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x08001107   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x0800110f   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x08001113   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x08001117   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x08001121   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x08001125   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x08001137   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x08001151   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x08001157   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x080011e1   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x08001223   Thumb Code     8  stm32f10x_gpio.o(.text)
    RCC_DeInit                               0x08001251   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x08001291   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x080012d7   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x0800130f   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08001347   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x0800135b   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x08001361   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x08001379   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x0800137f   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08001391   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x0800139b   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x080013ad   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x080013bf   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x080013d3   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x080013ed   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x080013f5   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x08001407   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x08001439   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x0800143f   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x0800144b   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x08001453   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x08001513   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x0800152d   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08001547   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08001561   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x0800157b   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x08001595   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x0800159d   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x080015a3   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x080015a9   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x080015b7   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x080015cb   Thumb Code     6  stm32f10x_rcc.o(.text)
    TIM_DeInit                               0x080015f5   Thumb Code   424  stm32f10x_tim.o(.text)
    TIM_TimeBaseInit                         0x0800179d   Thumb Code   122  stm32f10x_tim.o(.text)
    TIM_OC1Init                              0x08001817   Thumb Code   132  stm32f10x_tim.o(.text)
    TIM_OC2Init                              0x0800189b   Thumb Code   154  stm32f10x_tim.o(.text)
    TIM_OC3Init                              0x08001935   Thumb Code   150  stm32f10x_tim.o(.text)
    TIM_OC4Init                              0x080019cb   Thumb Code   182  stm32f10x_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08001a81   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08001b1d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08001ba9   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_SetIC1Prescaler                      0x08001c45   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ICInit                               0x08001cc3   Thumb Code   150  stm32f10x_tim.o(.text)
    TIM_PWMIConfig                           0x08001d59   Thumb Code   124  stm32f10x_tim.o(.text)
    TIM_BDTRConfig                           0x08001dd5   Thumb Code    32  stm32f10x_tim.o(.text)
    TIM_TimeBaseStructInit                   0x08001df5   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OCStructInit                         0x08001e07   Thumb Code    20  stm32f10x_tim.o(.text)
    TIM_ICStructInit                         0x08001e1b   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_BDTRStructInit                       0x08001e2d   Thumb Code    40  stm32f10x_tim.o(.text)
    TIM_Cmd                                  0x08001e55   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08001e6d   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_ITConfig                             0x08001e8b   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_GenerateEvent                        0x08001e9d   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_DMAConfig                            0x08001ea1   Thumb Code    10  stm32f10x_tim.o(.text)
    TIM_DMACmd                               0x08001eab   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_InternalClockConfig                  0x08001ebd   Thumb Code    12  stm32f10x_tim.o(.text)
    TIM_SelectInputTrigger                   0x08001ec9   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x08001edb   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_TIxExternalClockConfig               0x08001ef3   Thumb Code    62  stm32f10x_tim.o(.text)
    TIM_ETRConfig                            0x08001f31   Thumb Code    28  stm32f10x_tim.o(.text)
    TIM_ETRClockMode1Config                  0x08001f4d   Thumb Code    54  stm32f10x_tim.o(.text)
    TIM_ETRClockMode2Config                  0x08001f83   Thumb Code    32  stm32f10x_tim.o(.text)
    TIM_PrescalerConfig                      0x08001fa3   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_CounterModeConfig                    0x08001fa9   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08001fbb   Thumb Code    66  stm32f10x_tim.o(.text)
    TIM_ForcedOC1Config                      0x08001ffd   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ForcedOC2Config                      0x0800200f   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ForcedOC3Config                      0x08002029   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ForcedOC4Config                      0x0800203b   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08002055   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectCOM                            0x0800206d   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectCCDMA                          0x08002085   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_CCPreloadControl                     0x0800209d   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_OC1PreloadConfig                     0x080020b5   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2PreloadConfig                     0x080020c7   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3PreloadConfig                     0x080020e1   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC4PreloadConfig                     0x080020f3   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC1FastConfig                        0x0800210d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2FastConfig                        0x0800211f   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3FastConfig                        0x08002139   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC4FastConfig                        0x0800214b   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ClearOC1Ref                          0x08002165   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearOC2Ref                          0x08002177   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_ClearOC3Ref                          0x0800218f   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearOC4Ref                          0x080021a1   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_OC1PolarityConfig                    0x080021b9   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x080021cb   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2PolarityConfig                    0x080021dd   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x080021f7   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3PolarityConfig                    0x08002211   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x0800222b   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC4PolarityConfig                    0x08002245   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_CCxCmd                               0x0800225f   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_CCxNCmd                              0x0800227d   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_SelectOCxM                           0x0800229b   Thumb Code    82  stm32f10x_tim.o(.text)
    TIM_UpdateDisableConfig                  0x080022ed   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_UpdateRequestConfig                  0x08002305   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectHallSensor                     0x0800231d   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectOnePulseMode                   0x08002335   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08002347   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectSlaveMode                      0x08002359   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x0800236b   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SetCounter                           0x0800237d   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetAutoreload                        0x08002381   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare1                          0x08002385   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare2                          0x08002389   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare3                          0x0800238d   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare4                          0x08002391   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_SetClockDivision                     0x08002397   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_GetCapture1                          0x080023a9   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture2                          0x080023af   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture3                          0x080023b5   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture4                          0x080023bb   Thumb Code     8  stm32f10x_tim.o(.text)
    TIM_GetCounter                           0x080023c3   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetPrescaler                         0x080023c9   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetFlagStatus                        0x080023cf   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearFlag                            0x080023e1   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetITStatus                          0x080023e7   Thumb Code    34  stm32f10x_tim.o(.text)
    TIM_ClearITPendingBit                    0x08002409   Thumb Code     6  stm32f10x_tim.o(.text)
    SystemInit                               0x080024ef   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x0800253d   Thumb Code   142  system_stm32f10x.o(.text)
    Reset_Handler                            0x080025f1   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    NMI_Handler                              0x080025f9   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    HardFault_Handler                        0x080025fb   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    MemManage_Handler                        0x080025fd   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    BusFault_Handler                         0x080025ff   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    UsageFault_Handler                       0x08002601   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SVC_Handler                              0x08002603   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    DebugMon_Handler                         0x08002605   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    PendSV_Handler                           0x08002607   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SysTick_Handler                          0x08002609   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800260b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_ldivmod                          0x08002615   Thumb Code    98  ldiv.o(.text)
    __aeabi_fadd                             0x08002677   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x0800271b   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x08002721   Thumb Code     6  fadd.o(.text)
    __aeabi_fdiv                             0x08002727   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x080027a3   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080028e5   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080028eb   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080028f1   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080029d5   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x08002ab3   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x08002ad5   Thumb Code    26  dfltui.o(.text)
    __aeabi_f2uiz                            0x08002aef   Thumb Code    40  ffixui.o(.text)
    __aeabi_d2uiz                            0x08002b17   Thumb Code    50  dfixui.o(.text)
    __aeabi_uldivmod                         0x08002b49   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08002bab   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08002bab   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08002bc9   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08002bc9   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08002be9   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08002be9   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08002c0d   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08002c0d   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08002c1f   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08002c7b   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08002c99   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x08002d35   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08002d35   Thumb Code     0  init.o(.text)
    __scatterload_copy                       0x08002d59   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08002d67   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08002d69   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x08002d78   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002d98   Number         0  anon$$obj.o(Region$$Table)
    Fn                                       0x20000000   Data           1  main.o(.data)
    counterf                                 0x20000004   Data           4  pwmoutput.o(.data)
    divf                                     0x20000008   Data           2  pwmoutput.o(.data)
    radio_num                                0x2000000a   Data           2  pwmoutput.o(.data)
    CGTab                                    0x2000000c   Data          64  charlcd.o(.data)
    tab1                                     0x2000004c   Data          16  charlcd.o(.data)
    tab2                                     0x2000005c   Data          15  charlcd.o(.data)
    tabdy                                    0x2000006b   Data          32  charlcd.o(.data)
    lcdbuff                                  0x2000008b   Data           1  charlcd.o(.data)
    lcdbuff_1                                0x2000008c   Data           1  charlcd.o(.data)
    SystemCoreClock                          0x200000a4   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x200000a8   Data          16  system_stm32f10x.o(.data)
    __initial_sp                             0x200004b8   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002e50, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002d98, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          252    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO          257  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO          280    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO          283    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO          285    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO          287    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO          288    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO          290    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO          292    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO          281    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000150   Code   RO            1    .text               main.o
    0x08000294   0x08000294   0x00000128   Code   RO           73    .text               delay.o
    0x080003bc   0x080003bc   0x00000050   Code   RO           88    .text               peripheralinit.o
    0x0800040c   0x0800040c   0x00000304   Code   RO          100    .text               max262.o
    0x08000710   0x08000710   0x000000bc   Code   RO          117    .text               pwmoutput.o
    0x080007cc   0x080007cc   0x000000d0   Code   RO          132    .text               key.o
    0x0800089c   0x0800089c   0x00000658   Code   RO          144    .text               charlcd.o
    0x08000ef4   0x08000ef4   0x0000035c   Code   RO          171    .text               stm32f10x_gpio.o
    0x08001250   0x08001250   0x000003a4   Code   RO          183    .text               stm32f10x_rcc.o
    0x080015f4   0x080015f4   0x00000e1a   Code   RO          197    .text               stm32f10x_tim.o
    0x0800240e   0x0800240e   0x00000002   PAD
    0x08002410   0x08002410   0x000001e0   Code   RO          232    .text               system_stm32f10x.o
    0x080025f0   0x080025f0   0x00000024   Code   RO          253    .text               startup_stm32f10x_hd.o
    0x08002614   0x08002614   0x00000062   Code   RO          260    .text               mc_w.l(ldiv.o)
    0x08002676   0x08002676   0x000000b0   Code   RO          262    .text               mf_w.l(fadd.o)
    0x08002726   0x08002726   0x0000007c   Code   RO          264    .text               mf_w.l(fdiv.o)
    0x080027a2   0x080027a2   0x0000014e   Code   RO          266    .text               mf_w.l(dadd.o)
    0x080028f0   0x080028f0   0x000000e4   Code   RO          268    .text               mf_w.l(dmul.o)
    0x080029d4   0x080029d4   0x000000de   Code   RO          270    .text               mf_w.l(ddiv.o)
    0x08002ab2   0x08002ab2   0x00000022   Code   RO          272    .text               mf_w.l(dflti.o)
    0x08002ad4   0x08002ad4   0x0000001a   Code   RO          274    .text               mf_w.l(dfltui.o)
    0x08002aee   0x08002aee   0x00000028   Code   RO          276    .text               mf_w.l(ffixui.o)
    0x08002b16   0x08002b16   0x00000032   Code   RO          278    .text               mf_w.l(dfixui.o)
    0x08002b48   0x08002b48   0x00000062   Code   RO          294    .text               mc_w.l(uldiv.o)
    0x08002baa   0x08002baa   0x0000001e   Code   RO          296    .text               mc_w.l(llshl.o)
    0x08002bc8   0x08002bc8   0x00000020   Code   RO          298    .text               mc_w.l(llushr.o)
    0x08002be8   0x08002be8   0x00000024   Code   RO          300    .text               mc_w.l(llsshr.o)
    0x08002c0c   0x08002c0c   0x00000000   Code   RO          302    .text               mc_w.l(iusefp.o)
    0x08002c0c   0x08002c0c   0x0000006e   Code   RO          303    .text               mf_w.l(fepilogue.o)
    0x08002c7a   0x08002c7a   0x000000ba   Code   RO          305    .text               mf_w.l(depilogue.o)
    0x08002d34   0x08002d34   0x00000024   Code   RO          307    .text               mc_w.l(init.o)
    0x08002d58   0x08002d58   0x0000000e   Code   RO          311    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08002d66   0x08002d66   0x00000002   Code   RO          312    i.__scatterload_null  mc_w.l(handlers.o)
    0x08002d68   0x08002d68   0x0000000e   Code   RO          313    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08002d76   0x08002d76   0x00000002   PAD
    0x08002d78   0x08002d78   0x00000020   Data   RO          309    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002d98, Size: 0x000004b8, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002d98   0x00000001   Data   RW            2    .data               main.o
    0x20000001   0x08002d99   0x00000003   PAD
    0x20000004   0x08002d9c   0x00000008   Data   RW          118    .data               pwmoutput.o
    0x2000000c   0x08002da4   0x00000081   Data   RW          145    .data               charlcd.o
    0x2000008d   0x08002e25   0x00000014   Data   RW          184    .data               stm32f10x_rcc.o
    0x200000a1   0x08002e39   0x00000003   PAD
    0x200000a4   0x08002e3c   0x00000014   Data   RW          233    .data               system_stm32f10x.o
    0x200000b8        -       0x00000400   Zero   RW          250    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1624         88          0        129          0       5208   charlcd.o
         0          0          0          0          0         32   core_cm3.o
       296          0          0          0          0       2661   delay.o
       208          6          0          0          0        784   key.o
       336         62          0          1          0     246616   main.o
       772         12          0          0          0       1429   max262.o
        80          8          0          0          0        714   peripheralinit.o
       188         18          0          8          0       1334   pwmoutput.o
        36          8        304          0       1024        900   startup_stm32f10x_hd.o
       860         38          0          0          0       5941   stm32f10x_gpio.o
       932         36          0         20          0       9204   stm32f10x_rcc.o
      3610         88          0          0          0      23052   stm32f10x_tim.o
       480         38          0         20          0       2163   system_stm32f10x.o

    ----------------------------------------------------------------------
      9424        <USER>        <GROUP>        184       1024     300038   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          6          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        98          0          0          0          0         84   ldiv.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        98          0          0          0          0         92   uldiv.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        50          0          0          0          0         76   dfixui.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        40          0          0          0          0         68   ffixui.o

    ----------------------------------------------------------------------
      1912         <USER>          <GROUP>          0          0       1660   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       380         16          0          0          0        448   mc_w.l
      1530          0          0          0          0       1212   mf_w.l

    ----------------------------------------------------------------------
      1912         <USER>          <GROUP>          0          0       1660   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     11336        418        336        184       1024     300186   Grand Totals
     11336        418        336        184       1024     300186   ELF Image Totals
     11336        418        336        184          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                11672 (  11.40kB)
    Total RW  Size (RW Data + ZI Data)              1208 (   1.18kB)
    Total ROM Size (Code + RO Data + RW Data)      11856 (  11.58kB)

==============================================================================

