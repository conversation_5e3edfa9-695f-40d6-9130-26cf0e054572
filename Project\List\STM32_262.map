Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(.text) refers to peripheralinit.o(.text) for PeripheralInit
    main.o(.text) refers to delay.o(.text) for Delay_1ms
    main.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    main.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    main.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    main.o(.text) refers to dfixui.o(.text) for __aeabi_d2uiz
    main.o(.text) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(.text) refers to pwmoutput.o(.text) for TIM2_PWMOutput_Init
    main.o(.text) refers to max262.o(.text) for Filter1
    main.o(.text) refers to main.o(.data) for uart_data_ready
    main.o(.text) refers to pwmoutput.o(.data) for divf
    peripheralinit.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    peripheralinit.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    peripheralinit.o(.text) refers to stm32f10x_usart.o(.text) for USART_Init
    peripheralinit.o(.text) refers to misc.o(.text) for NVIC_Init
    peripheralinit.o(.text) refers to charlcd.o(.text) for LCM_Init
    peripheralinit.o(.text) refers to key.o(.text) for Key_Init
    peripheralinit.o(.text) refers to pwmoutput.o(.text) for TIM2_PWMOutput_Init
    max262.o(.text) refers to fdiv.o(.text) for __aeabi_fdiv
    max262.o(.text) refers to fadd.o(.text) for __aeabi_frsub
    max262.o(.text) refers to ffixui.o(.text) for __aeabi_f2uiz
    max262.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    max262.o(.text) refers to delay.o(.text) for Delay_ns
    max262.o(.text) refers to main.o(.data) for Fn
    pwmoutput.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    pwmoutput.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    pwmoutput.o(.text) refers to stm32f10x_tim.o(.text) for TIM_TimeBaseInit
    pwmoutput.o(.text) refers to pwmoutput.o(.data) for counterf
    key.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    key.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to delay.o(.text) for Delay_1ms
    charlcd.o(.text) refers to delay.o(.text) for Delay_ns
    charlcd.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    charlcd.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    charlcd.o(.text) refers to ldiv.o(.text) for __aeabi_ldivmod
    charlcd.o(.text) refers to charlcd.o(.data) for lcdbuff
    stm32f10x_it.o(.text) refers to stm32f10x_usart.o(.text) for USART_GetITStatus
    stm32f10x_it.o(.text) refers to atoi.o(.text) for atoi
    stm32f10x_it.o(.text) refers to atof.o(i.atof) for atof
    stm32f10x_it.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    stm32f10x_it.o(.text) refers to stm32f10x_it.o(.data) for uart_rx_index
    stm32f10x_it.o(.text) refers to stm32f10x_it.o(.bss) for uart_rx_buffer
    stm32f10x_it.o(.text) refers to main.o(.data) for uart_fre1
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_tim.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text) for NMI_Handler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_o.o(.text) for isspace
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing stm32f10x_fsmc.o(.text), (1548 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).

3 unused section(s) (total 2092 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ..\CMSIS\CoreSupport\core_cm3.c          0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s 0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.c 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c 0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\User\CharLCD.c                        0x00000000   Number         0  charlcd.o ABSOLUTE
    ..\User\Delay.c                          0x00000000   Number         0  delay.o ABSOLUTE
    ..\User\MAX262.c                         0x00000000   Number         0  max262.o ABSOLUTE
    ..\User\PWMOutput.c                      0x00000000   Number         0  pwmoutput.o ABSOLUTE
    ..\User\PeripheralInit.c                 0x00000000   Number         0  peripheralinit.o ABSOLUTE
    ..\User\key.c                            0x00000000   Number         0  key.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\stm32f10x_it.c                   0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    ..\\CMSIS\\CoreSupport\\core_cm3.c       0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section        0  main.o(.text)
    .text                                    0x080003bc   Section        0  delay.o(.text)
    .text                                    0x080004e4   Section        0  peripheralinit.o(.text)
    .text                                    0x080005fc   Section        0  max262.o(.text)
    .text                                    0x08000900   Section        0  pwmoutput.o(.text)
    .text                                    0x080009bc   Section        0  key.o(.text)
    .text                                    0x08000a8c   Section        0  charlcd.o(.text)
    .text                                    0x080010e4   Section        0  stm32f10x_it.o(.text)
    .text                                    0x080011d0   Section        0  misc.o(.text)
    .text                                    0x080012ac   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x08001608   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x080019ac   Section        0  stm32f10x_tim.o(.text)
    TI4_Config                               0x08001e53   Thumb Code   130  stm32f10x_tim.o(.text)
    TI3_Config                               0x08001ee7   Thumb Code   122  stm32f10x_tim.o(.text)
    TI2_Config                               0x08001f7b   Thumb Code   130  stm32f10x_tim.o(.text)
    TI1_Config                               0x0800200f   Thumb Code   108  stm32f10x_tim.o(.text)
    .text                                    0x080027c8   Section        0  stm32f10x_usart.o(.text)
    .text                                    0x08002bd0   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x08002bd1   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x08002ca7   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x08002db0   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08002dd4   Section        0  ldiv.o(.text)
    .text                                    0x08002e36   Section        0  atoi.o(.text)
    .text                                    0x08002e50   Section        0  fadd.o(.text)
    .text                                    0x08002f00   Section        0  fdiv.o(.text)
    .text                                    0x08002f7c   Section        0  dadd.o(.text)
    .text                                    0x080030ca   Section        0  dmul.o(.text)
    .text                                    0x080031ae   Section        0  ddiv.o(.text)
    .text                                    0x0800328c   Section        0  dflti.o(.text)
    .text                                    0x080032ae   Section        0  dfltui.o(.text)
    .text                                    0x080032c8   Section        0  ffixui.o(.text)
    .text                                    0x080032f0   Section        0  dfixui.o(.text)
    .text                                    0x08003322   Section        0  d2f.o(.text)
    .text                                    0x0800335a   Section        0  uldiv.o(.text)
    .text                                    0x080033bc   Section        0  llshl.o(.text)
    .text                                    0x080033da   Section        0  llushr.o(.text)
    .text                                    0x080033fa   Section        0  llsshr.o(.text)
    .text                                    0x08003420   Section        0  strtod.o(.text)
    _local_sscanf                            0x08003421   Thumb Code    54  strtod.o(.text)
    .text                                    0x080034bc   Section        0  strtol.o(.text)
    .text                                    0x0800352c   Section        0  iusefp.o(.text)
    .text                                    0x0800352c   Section        0  fepilogue.o(.text)
    .text                                    0x0800359a   Section        0  depilogue.o(.text)
    .text                                    0x08003654   Section       36  init.o(.text)
    .text                                    0x08003678   Section        0  ctype_o.o(.text)
    .text                                    0x08003680   Section        0  isspace_o.o(.text)
    .text                                    0x08003694   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08003695   Thumb Code   296  scanf_fp.o(.text)
    .text                                    0x080039f4   Section        0  _sgetc.o(.text)
    .text                                    0x08003a34   Section        0  _strtoul.o(.text)
    .text                                    0x08003ad2   Section        0  _chval.o(.text)
    .text                                    0x08003aee   Section        0  dfltul.o(.text)
    i.__aeabi_errno_addr                     0x08003b08   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__read_errno                           0x08003b10   Section        0  errno.o(i.__read_errno)
    i.__scatterload_copy                     0x08003b1c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08003b2a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08003b2c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08003b3c   Section        0  errno.o(i.__set_errno)
    i._is_digit                              0x08003b48   Section        0  scanf_fp.o(i._is_digit)
    i.atof                                   0x08003b56   Section        0  atof.o(i.atof)
    .constdata                               0x08003b80   Section      129  ctype_o.o(.constdata)
    .constdata                               0x08003c04   Section        4  ctype_o.o(.constdata)
    table                                    0x08003c04   Data           4  ctype_o.o(.constdata)
    .data                                    0x20000000   Section       14  main.o(.data)
    .data                                    0x20000010   Section        8  pwmoutput.o(.data)
    .data                                    0x20000018   Section      129  charlcd.o(.data)
    .data                                    0x20000099   Section        2  stm32f10x_it.o(.data)
    uart_rx_index                            0x20000099   Data           1  stm32f10x_it.o(.data)
    uart_rx_state                            0x2000009a   Data           1  stm32f10x_it.o(.data)
    .data                                    0x2000009b   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000009b   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x200000ab   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x200000b0   Section       20  system_stm32f10x.o(.data)
    .data                                    0x200000c4   Section        4  errno.o(.data)
    _errno                                   0x200000c4   Data           4  errno.o(.data)
    .bss                                     0x200000c8   Section       32  stm32f10x_it.o(.bss)
    uart_rx_buffer                           0x200000c8   Data          32  stm32f10x_it.o(.bss)
    STACK                                    0x200000e8   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    main                                     0x08000145   Thumb Code   350  main.o(.text)
    TimingDelay_Decrement                    0x080003bd   Thumb Code     2  delay.o(.text)
    Delay_ns                                 0x080003bf   Thumb Code    12  delay.o(.text)
    Delay_1us                                0x080003cb   Thumb Code    26  delay.o(.text)
    Delay_2us                                0x080003e5   Thumb Code    26  delay.o(.text)
    Delay_10us                               0x080003ff   Thumb Code    36  delay.o(.text)
    Delay_250us                              0x08000423   Thumb Code    36  delay.o(.text)
    Delay_882us                              0x08000447   Thumb Code    26  delay.o(.text)
    Delay_1ms                                0x08000461   Thumb Code    36  delay.o(.text)
    Delay_5ms                                0x08000485   Thumb Code    38  delay.o(.text)
    Delay_50ms                               0x080004ab   Thumb Code    40  delay.o(.text)
    Delay                                    0x080004d3   Thumb Code    18  delay.o(.text)
    USART1_Init                              0x080004e5   Thumb Code   154  peripheralinit.o(.text)
    PeripheralInit                           0x0800057f   Thumb Code    20  peripheralinit.o(.text)
    MAX262_GPIO_Init                         0x08000593   Thumb Code    56  peripheralinit.o(.text)
    USART1_SendString                        0x080005cb   Thumb Code    38  peripheralinit.o(.text)
    Qn                                       0x080005fd   Thumb Code    40  max262.o(.text)
    Filter1                                  0x08000625   Thumb Code   348  max262.o(.text)
    Filter2                                  0x08000781   Thumb Code   372  max262.o(.text)
    TIM2_GPIO_Init                           0x08000901   Thumb Code    38  pwmoutput.o(.text)
    TIM2_Mode_Init                           0x08000927   Thumb Code   120  pwmoutput.o(.text)
    TIM2_PWMOutput_Init                      0x0800099f   Thumb Code    12  pwmoutput.o(.text)
    Key_Init                                 0x080009bd   Thumb Code    38  key.o(.text)
    Key_Scan                                 0x080009e3   Thumb Code   164  key.o(.text)
    Wr_CodeData                              0x08000a8d   Thumb Code   166  charlcd.o(.text)
    WrCLcdD                                  0x08000b33   Thumb Code    38  charlcd.o(.text)
    WrCLcdC                                  0x08000b59   Thumb Code    38  charlcd.o(.text)
    CG_Write                                 0x08000b7f   Thumb Code    30  charlcd.o(.text)
    GPIO_LCM_Configuration                   0x08000b9d   Thumb Code    64  charlcd.o(.text)
    LCM_Init                                 0x08000bdd   Thumb Code    60  charlcd.o(.text)
    WriteString                              0x08000c19   Thumb Code    52  charlcd.o(.text)
    WrCLcd_char_num                          0x08000c4d   Thumb Code   174  charlcd.o(.text)
    WrCLcd_int_num                           0x08000cfb   Thumb Code   226  charlcd.o(.text)
    WrCLcd_long_num                          0x08000ddd   Thumb Code   454  charlcd.o(.text)
    Wr_In1                                   0x08000fa3   Thumb Code    36  charlcd.o(.text)
    Wr_In2                                   0x08000fc7   Thumb Code    36  charlcd.o(.text)
    CL_Enter                                 0x08000feb   Thumb Code    44  charlcd.o(.text)
    CR_Enter                                 0x08001017   Thumb Code    42  charlcd.o(.text)
    L_Enter                                  0x08001041   Thumb Code    42  charlcd.o(.text)
    R_Enter                                  0x0800106b   Thumb Code    42  charlcd.o(.text)
    CGWrite                                  0x08001095   Thumb Code    62  charlcd.o(.text)
    NMI_Handler                              0x080010e5   Thumb Code     2  stm32f10x_it.o(.text)
    HardFault_Handler                        0x080010e7   Thumb Code     4  stm32f10x_it.o(.text)
    MemManage_Handler                        0x080010eb   Thumb Code     4  stm32f10x_it.o(.text)
    BusFault_Handler                         0x080010ef   Thumb Code     4  stm32f10x_it.o(.text)
    UsageFault_Handler                       0x080010f3   Thumb Code     4  stm32f10x_it.o(.text)
    SVC_Handler                              0x080010f7   Thumb Code     2  stm32f10x_it.o(.text)
    DebugMon_Handler                         0x080010f9   Thumb Code     2  stm32f10x_it.o(.text)
    PendSV_Handler                           0x080010fb   Thumb Code     2  stm32f10x_it.o(.text)
    SysTick_Handler                          0x080010fd   Thumb Code     2  stm32f10x_it.o(.text)
    USART1_IRQHandler                        0x080010ff   Thumb Code   178  stm32f10x_it.o(.text)
    NVIC_PriorityGroupConfig                 0x080011d1   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x080011db   Thumb Code   100  misc.o(.text)
    NVIC_SetVectorTable                      0x0800123f   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x0800124d   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x0800126f   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x080012ad   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08001359   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x0800136d   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x08001483   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08001493   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x080014a5   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x080014ad   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x080014bf   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x080014c7   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x080014cb   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x080014cf   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x080014d9   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x080014dd   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x080014ef   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x08001509   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x0800150f   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x08001599   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x080015db   Thumb Code     8  stm32f10x_gpio.o(.text)
    RCC_DeInit                               0x08001609   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x08001649   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x0800168f   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x080016c7   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x080016ff   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x08001713   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x08001719   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x08001731   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08001737   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08001749   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x08001753   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x08001765   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x08001777   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x0800178b   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x080017a5   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x080017ad   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x080017bf   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x080017f1   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x080017f7   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08001803   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x0800180b   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x080018cb   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x080018e5   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x080018ff   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08001919   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08001933   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x0800194d   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08001955   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x0800195b   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x08001961   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x0800196f   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08001983   Thumb Code     6  stm32f10x_rcc.o(.text)
    TIM_DeInit                               0x080019ad   Thumb Code   424  stm32f10x_tim.o(.text)
    TIM_TimeBaseInit                         0x08001b55   Thumb Code   122  stm32f10x_tim.o(.text)
    TIM_OC1Init                              0x08001bcf   Thumb Code   132  stm32f10x_tim.o(.text)
    TIM_OC2Init                              0x08001c53   Thumb Code   154  stm32f10x_tim.o(.text)
    TIM_OC3Init                              0x08001ced   Thumb Code   150  stm32f10x_tim.o(.text)
    TIM_OC4Init                              0x08001d83   Thumb Code   182  stm32f10x_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08001e39   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08001ed5   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08001f61   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_SetIC1Prescaler                      0x08001ffd   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ICInit                               0x0800207b   Thumb Code   150  stm32f10x_tim.o(.text)
    TIM_PWMIConfig                           0x08002111   Thumb Code   124  stm32f10x_tim.o(.text)
    TIM_BDTRConfig                           0x0800218d   Thumb Code    32  stm32f10x_tim.o(.text)
    TIM_TimeBaseStructInit                   0x080021ad   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OCStructInit                         0x080021bf   Thumb Code    20  stm32f10x_tim.o(.text)
    TIM_ICStructInit                         0x080021d3   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_BDTRStructInit                       0x080021e5   Thumb Code    40  stm32f10x_tim.o(.text)
    TIM_Cmd                                  0x0800220d   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08002225   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_ITConfig                             0x08002243   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_GenerateEvent                        0x08002255   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_DMAConfig                            0x08002259   Thumb Code    10  stm32f10x_tim.o(.text)
    TIM_DMACmd                               0x08002263   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_InternalClockConfig                  0x08002275   Thumb Code    12  stm32f10x_tim.o(.text)
    TIM_SelectInputTrigger                   0x08002281   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x08002293   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_TIxExternalClockConfig               0x080022ab   Thumb Code    62  stm32f10x_tim.o(.text)
    TIM_ETRConfig                            0x080022e9   Thumb Code    28  stm32f10x_tim.o(.text)
    TIM_ETRClockMode1Config                  0x08002305   Thumb Code    54  stm32f10x_tim.o(.text)
    TIM_ETRClockMode2Config                  0x0800233b   Thumb Code    32  stm32f10x_tim.o(.text)
    TIM_PrescalerConfig                      0x0800235b   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_CounterModeConfig                    0x08002361   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08002373   Thumb Code    66  stm32f10x_tim.o(.text)
    TIM_ForcedOC1Config                      0x080023b5   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ForcedOC2Config                      0x080023c7   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ForcedOC3Config                      0x080023e1   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ForcedOC4Config                      0x080023f3   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ARRPreloadConfig                     0x0800240d   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectCOM                            0x08002425   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectCCDMA                          0x0800243d   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_CCPreloadControl                     0x08002455   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_OC1PreloadConfig                     0x0800246d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2PreloadConfig                     0x0800247f   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3PreloadConfig                     0x08002499   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC4PreloadConfig                     0x080024ab   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC1FastConfig                        0x080024c5   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2FastConfig                        0x080024d7   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3FastConfig                        0x080024f1   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC4FastConfig                        0x08002503   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ClearOC1Ref                          0x0800251d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearOC2Ref                          0x0800252f   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_ClearOC3Ref                          0x08002547   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearOC4Ref                          0x08002559   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_OC1PolarityConfig                    0x08002571   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x08002583   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2PolarityConfig                    0x08002595   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x080025af   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3PolarityConfig                    0x080025c9   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x080025e3   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC4PolarityConfig                    0x080025fd   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_CCxCmd                               0x08002617   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_CCxNCmd                              0x08002635   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_SelectOCxM                           0x08002653   Thumb Code    82  stm32f10x_tim.o(.text)
    TIM_UpdateDisableConfig                  0x080026a5   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_UpdateRequestConfig                  0x080026bd   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectHallSensor                     0x080026d5   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectOnePulseMode                   0x080026ed   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectOutputTrigger                  0x080026ff   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectSlaveMode                      0x08002711   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08002723   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SetCounter                           0x08002735   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetAutoreload                        0x08002739   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare1                          0x0800273d   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare2                          0x08002741   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare3                          0x08002745   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare4                          0x08002749   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_SetClockDivision                     0x0800274f   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_GetCapture1                          0x08002761   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture2                          0x08002767   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture3                          0x0800276d   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture4                          0x08002773   Thumb Code     8  stm32f10x_tim.o(.text)
    TIM_GetCounter                           0x0800277b   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetPrescaler                         0x08002781   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetFlagStatus                        0x08002787   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearFlag                            0x08002799   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetITStatus                          0x0800279f   Thumb Code    34  stm32f10x_tim.o(.text)
    TIM_ClearITPendingBit                    0x080027c1   Thumb Code     6  stm32f10x_tim.o(.text)
    USART_DeInit                             0x080027c9   Thumb Code   134  stm32f10x_usart.o(.text)
    USART_Init                               0x0800284f   Thumb Code   210  stm32f10x_usart.o(.text)
    USART_StructInit                         0x08002921   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ClockInit                          0x08002939   Thumb Code    34  stm32f10x_usart.o(.text)
    USART_ClockStructInit                    0x0800295b   Thumb Code    12  stm32f10x_usart.o(.text)
    USART_Cmd                                0x08002967   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ITConfig                           0x0800297f   Thumb Code    74  stm32f10x_usart.o(.text)
    USART_DMACmd                             0x080029c9   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_SetAddress                         0x080029db   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_WakeUpConfig                       0x080029ed   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x080029ff   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08002a17   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_LINCmd                             0x08002a29   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SendData                           0x08002a41   Thumb Code     8  stm32f10x_usart.o(.text)
    USART_ReceiveData                        0x08002a49   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SendBreak                          0x08002a53   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SetGuardTime                       0x08002a5d   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SetPrescaler                       0x08002a6d   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SmartCardCmd                       0x08002a7d   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08002a95   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_HalfDuplexCmd                      0x08002aad   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_OverSampling8Cmd                   0x08002ac5   Thumb Code    22  stm32f10x_usart.o(.text)
    USART_OneBitMethodCmd                    0x08002adb   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_IrDAConfig                         0x08002af3   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_IrDACmd                            0x08002b05   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_GetFlagStatus                      0x08002b1d   Thumb Code    26  stm32f10x_usart.o(.text)
    USART_ClearFlag                          0x08002b37   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_GetITStatus                        0x08002b49   Thumb Code    84  stm32f10x_usart.o(.text)
    USART_ClearITPendingBit                  0x08002b9d   Thumb Code    52  stm32f10x_usart.o(.text)
    SystemInit                               0x08002caf   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x08002cfd   Thumb Code   142  system_stm32f10x.o(.text)
    Reset_Handler                            0x08002db1   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08002dcb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_ldivmod                          0x08002dd5   Thumb Code    98  ldiv.o(.text)
    atoi                                     0x08002e37   Thumb Code    26  atoi.o(.text)
    __aeabi_fadd                             0x08002e51   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x08002ef5   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x08002efb   Thumb Code     6  fadd.o(.text)
    __aeabi_fdiv                             0x08002f01   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x08002f7d   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080030bf   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080030c5   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080030cb   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080031af   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x0800328d   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x080032af   Thumb Code    26  dfltui.o(.text)
    __aeabi_f2uiz                            0x080032c9   Thumb Code    40  ffixui.o(.text)
    __aeabi_d2uiz                            0x080032f1   Thumb Code    50  dfixui.o(.text)
    __aeabi_d2f                              0x08003323   Thumb Code    56  d2f.o(.text)
    __aeabi_uldivmod                         0x0800335b   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x080033bd   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080033bd   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080033db   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080033db   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x080033fb   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080033fb   Thumb Code     0  llsshr.o(.text)
    __strtod_int                             0x08003457   Thumb Code    90  strtod.o(.text)
    strtol                                   0x080034bd   Thumb Code   112  strtol.o(.text)
    __I$use$fp                               0x0800352d   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0800352d   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800353f   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x0800359b   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080035b9   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x08003655   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08003655   Thumb Code     0  init.o(.text)
    __rt_ctype_table                         0x08003679   Thumb Code     4  ctype_o.o(.text)
    isspace                                  0x08003681   Thumb Code    18  isspace_o.o(.text)
    _scanf_real                              0x080037bd   Thumb Code     0  scanf_fp.o(.text)
    _scanf_really_real                       0x080037bd   Thumb Code   556  scanf_fp.o(.text)
    _sgetc                                   0x080039f5   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08003a13   Thumb Code    34  _sgetc.o(.text)
    _strtoul                                 0x08003a35   Thumb Code   158  _strtoul.o(.text)
    _chval                                   0x08003ad3   Thumb Code    28  _chval.o(.text)
    __aeabi_ul2d                             0x08003aef   Thumb Code    24  dfltul.o(.text)
    __aeabi_errno_addr                       0x08003b09   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x08003b09   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __read_errno                             0x08003b11   Thumb Code     6  errno.o(i.__read_errno)
    __scatterload_copy                       0x08003b1d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08003b2b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08003b2d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08003b3d   Thumb Code     6  errno.o(i.__set_errno)
    _is_digit                                0x08003b49   Thumb Code    14  scanf_fp.o(i._is_digit)
    atof                                     0x08003b57   Thumb Code    42  atof.o(i.atof)
    __ctype_table                            0x08003b80   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x08003c08   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003c28   Number         0  anon$$obj.o(Region$$Table)
    Fn                                       0x20000000   Data           1  main.o(.data)
    uart_fre1                                0x20000004   Data           4  main.o(.data)
    uart_q                                   0x20000008   Data           4  main.o(.data)
    uart_mode                                0x2000000c   Data           1  main.o(.data)
    uart_data_ready                          0x2000000d   Data           1  main.o(.data)
    counterf                                 0x20000010   Data           4  pwmoutput.o(.data)
    divf                                     0x20000014   Data           2  pwmoutput.o(.data)
    radio_num                                0x20000016   Data           2  pwmoutput.o(.data)
    CGTab                                    0x20000018   Data          64  charlcd.o(.data)
    tab1                                     0x20000058   Data          16  charlcd.o(.data)
    tab2                                     0x20000068   Data          15  charlcd.o(.data)
    tabdy                                    0x20000077   Data          32  charlcd.o(.data)
    lcdbuff                                  0x20000097   Data           1  charlcd.o(.data)
    lcdbuff_1                                0x20000098   Data           1  charlcd.o(.data)
    SystemCoreClock                          0x200000b0   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x200000b4   Data          16  system_stm32f10x.o(.data)
    __initial_sp                             0x200004e8   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003cf0, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003c28, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          289    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO          298  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO          325    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO          328    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO          330    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO          332    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO          333    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO          335    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO          337    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO          326    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000278   Code   RO            1    .text               main.o
    0x080003bc   0x080003bc   0x00000128   Code   RO           79    .text               delay.o
    0x080004e4   0x080004e4   0x00000118   Code   RO           94    .text               peripheralinit.o
    0x080005fc   0x080005fc   0x00000304   Code   RO          106    .text               max262.o
    0x08000900   0x08000900   0x000000bc   Code   RO          123    .text               pwmoutput.o
    0x080009bc   0x080009bc   0x000000d0   Code   RO          138    .text               key.o
    0x08000a8c   0x08000a8c   0x00000658   Code   RO          150    .text               charlcd.o
    0x080010e4   0x080010e4   0x000000ec   Code   RO          165    .text               stm32f10x_it.o
    0x080011d0   0x080011d0   0x000000dc   Code   RO          184    .text               misc.o
    0x080012ac   0x080012ac   0x0000035c   Code   RO          196    .text               stm32f10x_gpio.o
    0x08001608   0x08001608   0x000003a4   Code   RO          208    .text               stm32f10x_rcc.o
    0x080019ac   0x080019ac   0x00000e1a   Code   RO          222    .text               stm32f10x_tim.o
    0x080027c6   0x080027c6   0x00000002   PAD
    0x080027c8   0x080027c8   0x00000408   Code   RO          246    .text               stm32f10x_usart.o
    0x08002bd0   0x08002bd0   0x000001e0   Code   RO          269    .text               system_stm32f10x.o
    0x08002db0   0x08002db0   0x00000024   Code   RO          290    .text               startup_stm32f10x_hd.o
    0x08002dd4   0x08002dd4   0x00000062   Code   RO          301    .text               mc_w.l(ldiv.o)
    0x08002e36   0x08002e36   0x0000001a   Code   RO          303    .text               mc_w.l(atoi.o)
    0x08002e50   0x08002e50   0x000000b0   Code   RO          305    .text               mf_w.l(fadd.o)
    0x08002f00   0x08002f00   0x0000007c   Code   RO          307    .text               mf_w.l(fdiv.o)
    0x08002f7c   0x08002f7c   0x0000014e   Code   RO          309    .text               mf_w.l(dadd.o)
    0x080030ca   0x080030ca   0x000000e4   Code   RO          311    .text               mf_w.l(dmul.o)
    0x080031ae   0x080031ae   0x000000de   Code   RO          313    .text               mf_w.l(ddiv.o)
    0x0800328c   0x0800328c   0x00000022   Code   RO          315    .text               mf_w.l(dflti.o)
    0x080032ae   0x080032ae   0x0000001a   Code   RO          317    .text               mf_w.l(dfltui.o)
    0x080032c8   0x080032c8   0x00000028   Code   RO          319    .text               mf_w.l(ffixui.o)
    0x080032f0   0x080032f0   0x00000032   Code   RO          321    .text               mf_w.l(dfixui.o)
    0x08003322   0x08003322   0x00000038   Code   RO          323    .text               mf_w.l(d2f.o)
    0x0800335a   0x0800335a   0x00000062   Code   RO          339    .text               mc_w.l(uldiv.o)
    0x080033bc   0x080033bc   0x0000001e   Code   RO          341    .text               mc_w.l(llshl.o)
    0x080033da   0x080033da   0x00000020   Code   RO          343    .text               mc_w.l(llushr.o)
    0x080033fa   0x080033fa   0x00000024   Code   RO          345    .text               mc_w.l(llsshr.o)
    0x0800341e   0x0800341e   0x00000002   PAD
    0x08003420   0x08003420   0x0000009c   Code   RO          354    .text               mc_w.l(strtod.o)
    0x080034bc   0x080034bc   0x00000070   Code   RO          356    .text               mc_w.l(strtol.o)
    0x0800352c   0x0800352c   0x00000000   Code   RO          358    .text               mc_w.l(iusefp.o)
    0x0800352c   0x0800352c   0x0000006e   Code   RO          359    .text               mf_w.l(fepilogue.o)
    0x0800359a   0x0800359a   0x000000ba   Code   RO          361    .text               mf_w.l(depilogue.o)
    0x08003654   0x08003654   0x00000024   Code   RO          363    .text               mc_w.l(init.o)
    0x08003678   0x08003678   0x00000008   Code   RO          367    .text               mc_w.l(ctype_o.o)
    0x08003680   0x08003680   0x00000012   Code   RO          389    .text               mc_w.l(isspace_o.o)
    0x08003692   0x08003692   0x00000002   PAD
    0x08003694   0x08003694   0x00000360   Code   RO          395    .text               mc_w.l(scanf_fp.o)
    0x080039f4   0x080039f4   0x00000040   Code   RO          399    .text               mc_w.l(_sgetc.o)
    0x08003a34   0x08003a34   0x0000009e   Code   RO          401    .text               mc_w.l(_strtoul.o)
    0x08003ad2   0x08003ad2   0x0000001c   Code   RO          406    .text               mc_w.l(_chval.o)
    0x08003aee   0x08003aee   0x00000018   Code   RO          408    .text               mf_w.l(dfltul.o)
    0x08003b06   0x08003b06   0x00000002   PAD
    0x08003b08   0x08003b08   0x00000008   Code   RO          347    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x08003b10   0x08003b10   0x0000000c   Code   RO          348    i.__read_errno      mc_w.l(errno.o)
    0x08003b1c   0x08003b1c   0x0000000e   Code   RO          412    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08003b2a   0x08003b2a   0x00000002   Code   RO          413    i.__scatterload_null  mc_w.l(handlers.o)
    0x08003b2c   0x08003b2c   0x0000000e   Code   RO          414    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08003b3a   0x08003b3a   0x00000002   PAD
    0x08003b3c   0x08003b3c   0x0000000c   Code   RO          349    i.__set_errno       mc_w.l(errno.o)
    0x08003b48   0x08003b48   0x0000000e   Code   RO          397    i._is_digit         mc_w.l(scanf_fp.o)
    0x08003b56   0x08003b56   0x0000002a   Code   RO          295    i.atof              m_ws.l(atof.o)
    0x08003b80   0x08003b80   0x00000081   Data   RO          368    .constdata          mc_w.l(ctype_o.o)
    0x08003c01   0x08003c01   0x00000003   PAD
    0x08003c04   0x08003c04   0x00000004   Data   RO          369    .constdata          mc_w.l(ctype_o.o)
    0x08003c08   0x08003c08   0x00000020   Data   RO          410    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003c28, Size: 0x000004e8, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003c28   0x0000000e   Data   RW            2    .data               main.o
    0x2000000e   0x08003c36   0x00000002   PAD
    0x20000010   0x08003c38   0x00000008   Data   RW          124    .data               pwmoutput.o
    0x20000018   0x08003c40   0x00000081   Data   RW          151    .data               charlcd.o
    0x20000099   0x08003cc1   0x00000002   Data   RW          167    .data               stm32f10x_it.o
    0x2000009b   0x08003cc3   0x00000014   Data   RW          209    .data               stm32f10x_rcc.o
    0x200000af   0x08003cd7   0x00000001   PAD
    0x200000b0   0x08003cd8   0x00000014   Data   RW          270    .data               system_stm32f10x.o
    0x200000c4   0x08003cec   0x00000004   Data   RW          350    .data               mc_w.l(errno.o)
    0x200000c8        -       0x00000020   Zero   RW          166    .bss                stm32f10x_it.o
    0x200000e8        -       0x00000400   Zero   RW          287    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1624         88          0        129          0       5208   charlcd.o
         0          0          0          0          0         32   core_cm3.o
       296          0          0          0          0       2661   delay.o
       208          6          0          0          0        784   key.o
       632        282          0         14          0     254807   main.o
       772         12          0          0          0       1429   max262.o
       220         22          0          0          0       2013   misc.o
       280         12          0          0          0       1248   peripheralinit.o
       188         18          0          8          0       1334   pwmoutput.o
        36          8        304          0       1024        900   startup_stm32f10x_hd.o
       860         38          0          0          0       5941   stm32f10x_gpio.o
       236         32          0          2         32       2601   stm32f10x_it.o
       932         36          0         20          0       9204   stm32f10x_rcc.o
      3610         88          0          0          0      23052   stm32f10x_tim.o
      1032         22          0          0          0       8668   stm32f10x_usart.o
       480         38          0         20          0       2163   system_stm32f10x.o

    ----------------------------------------------------------------------
     11408        <USER>        <GROUP>        196       1056     322045   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        42          0          0          0          0         84   atof.o
        28          0          0          0          0         68   _chval.o
        64          0          0          0          0         84   _sgetc.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        32         16          0          4          0        204   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        18          0          0          0          0         76   isspace_o.o
         0          0          0          0          0          0   iusefp.o
        98          0          0          0          0         84   ldiv.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       878         12          0          0          0        216   scanf_fp.o
       156         12          0          0          0        120   strtod.o
       112          0          0          0          0         88   strtol.o
        98          0          0          0          0         92   uldiv.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        50          0          0          0          0         76   dfixui.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
        24          0          0          0          0         76   dfltul.o
       228          0          0          0          0         96   dmul.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        40          0          0          0          0         68   ffixui.o

    ----------------------------------------------------------------------
      3520         <USER>        <GROUP>          4          0       3004   Library Totals
         8          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        42          0          0          0          0         84   m_ws.l
      1860         60        133          4          0       1544   mc_w.l
      1610          0          0          0          0       1376   mf_w.l

    ----------------------------------------------------------------------
      3520         <USER>        <GROUP>          4          0       3004   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     14928        762        472        200       1056     322445   Grand Totals
     14928        762        472        200       1056     322445   ELF Image Totals
     14928        762        472        200          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                15400 (  15.04kB)
    Total RW  Size (RW Data + ZI Data)              1256 (   1.23kB)
    Total ROM Size (Code + RO Data + RW Data)      15600 (  15.23kB)

==============================================================================

