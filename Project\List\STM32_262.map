Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(.text) refers to peripheralinit.o(.text) for PeripheralInit
    main.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    main.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    main.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    main.o(.text) refers to dfixui.o(.text) for __aeabi_d2uiz
    main.o(.text) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(.text) refers to pwmoutput.o(.text) for TIM2_PWMOutput_Init
    main.o(.text) refers to max262.o(.text) for Filter1
    main.o(.text) refers to delay.o(.text) for Delay_1ms
    main.o(.text) refers to main.o(.data) for uart_data_ready
    main.o(.text) refers to pwmoutput.o(.data) for divf
    peripheralinit.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    peripheralinit.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    peripheralinit.o(.text) refers to stm32f10x_usart.o(.text) for USART_Init
    peripheralinit.o(.text) refers to misc.o(.text) for NVIC_Init
    peripheralinit.o(.text) refers to charlcd.o(.text) for LCM_Init
    peripheralinit.o(.text) refers to key.o(.text) for Key_Init
    peripheralinit.o(.text) refers to pwmoutput.o(.text) for TIM2_PWMOutput_Init
    max262.o(.text) refers to fdiv.o(.text) for __aeabi_fdiv
    max262.o(.text) refers to fadd.o(.text) for __aeabi_frsub
    max262.o(.text) refers to ffixui.o(.text) for __aeabi_f2uiz
    max262.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    max262.o(.text) refers to delay.o(.text) for Delay_ns
    max262.o(.text) refers to main.o(.data) for Fn
    pwmoutput.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    pwmoutput.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    pwmoutput.o(.text) refers to stm32f10x_tim.o(.text) for TIM_TimeBaseInit
    pwmoutput.o(.text) refers to pwmoutput.o(.data) for counterf
    key.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    key.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to delay.o(.text) for Delay_1ms
    charlcd.o(.text) refers to delay.o(.text) for Delay_ns
    charlcd.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    charlcd.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    charlcd.o(.text) refers to ldiv.o(.text) for __aeabi_ldivmod
    charlcd.o(.text) refers to charlcd.o(.data) for lcdbuff
    stm32f10x_it.o(.text) refers to stm32f10x_usart.o(.text) for USART_GetITStatus
    stm32f10x_it.o(.text) refers to stm32f10x_it.o(.data) for uart_rx_index
    stm32f10x_it.o(.text) refers to stm32f10x_it.o(.bss) for uart_rx_buffer
    stm32f10x_it.o(.text) refers to main.o(.data) for uart_q
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_tim.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text) for NMI_Handler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing stm32f10x_fsmc.o(.text), (1548 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).

3 unused section(s) (total 2092 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\CMSIS\CoreSupport\core_cm3.c          0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s 0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.c 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c 0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\User\CharLCD.c                        0x00000000   Number         0  charlcd.o ABSOLUTE
    ..\User\Delay.c                          0x00000000   Number         0  delay.o ABSOLUTE
    ..\User\MAX262.c                         0x00000000   Number         0  max262.o ABSOLUTE
    ..\User\PWMOutput.c                      0x00000000   Number         0  pwmoutput.o ABSOLUTE
    ..\User\PeripheralInit.c                 0x00000000   Number         0  peripheralinit.o ABSOLUTE
    ..\User\key.c                            0x00000000   Number         0  key.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\stm32f10x_it.c                   0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    ..\\CMSIS\\CoreSupport\\core_cm3.c       0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section        0  main.o(.text)
    .text                                    0x080002b4   Section        0  delay.o(.text)
    .text                                    0x080003dc   Section        0  peripheralinit.o(.text)
    .text                                    0x08000500   Section        0  max262.o(.text)
    .text                                    0x08000804   Section        0  pwmoutput.o(.text)
    .text                                    0x080008c0   Section        0  key.o(.text)
    .text                                    0x08000990   Section        0  charlcd.o(.text)
    .text                                    0x08000fe8   Section        0  stm32f10x_it.o(.text)
    .text                                    0x0800108c   Section        0  misc.o(.text)
    .text                                    0x08001168   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x080014c4   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x08001868   Section        0  stm32f10x_tim.o(.text)
    TI4_Config                               0x08001d0f   Thumb Code   130  stm32f10x_tim.o(.text)
    TI3_Config                               0x08001da3   Thumb Code   122  stm32f10x_tim.o(.text)
    TI2_Config                               0x08001e37   Thumb Code   130  stm32f10x_tim.o(.text)
    TI1_Config                               0x08001ecb   Thumb Code   108  stm32f10x_tim.o(.text)
    .text                                    0x08002684   Section        0  stm32f10x_usart.o(.text)
    .text                                    0x08002a8c   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x08002a8d   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x08002b63   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x08002c6c   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08002c90   Section        0  ldiv.o(.text)
    .text                                    0x08002cf2   Section        0  fadd.o(.text)
    .text                                    0x08002da2   Section        0  fdiv.o(.text)
    .text                                    0x08002e1e   Section        0  dadd.o(.text)
    .text                                    0x08002f6c   Section        0  dmul.o(.text)
    .text                                    0x08003050   Section        0  ddiv.o(.text)
    .text                                    0x0800312e   Section        0  dflti.o(.text)
    .text                                    0x08003150   Section        0  dfltui.o(.text)
    .text                                    0x0800316a   Section        0  ffixui.o(.text)
    .text                                    0x08003192   Section        0  dfixui.o(.text)
    .text                                    0x080031c4   Section        0  uldiv.o(.text)
    .text                                    0x08003226   Section        0  llshl.o(.text)
    .text                                    0x08003244   Section        0  llushr.o(.text)
    .text                                    0x08003264   Section        0  llsshr.o(.text)
    .text                                    0x08003288   Section        0  iusefp.o(.text)
    .text                                    0x08003288   Section        0  fepilogue.o(.text)
    .text                                    0x080032f6   Section        0  depilogue.o(.text)
    .text                                    0x080033b0   Section       36  init.o(.text)
    i.__scatterload_copy                     0x080033d4   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080033e2   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080033e4   Section       14  handlers.o(i.__scatterload_zeroinit)
    .data                                    0x20000000   Section       24  main.o(.data)
    .data                                    0x20000018   Section        8  pwmoutput.o(.data)
    .data                                    0x20000020   Section      129  charlcd.o(.data)
    .data                                    0x200000a1   Section        1  stm32f10x_it.o(.data)
    uart_rx_index                            0x200000a1   Data           1  stm32f10x_it.o(.data)
    .data                                    0x200000a2   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x200000a2   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x200000b2   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x200000b8   Section       20  system_stm32f10x.o(.data)
    .bss                                     0x200000cc   Section        9  stm32f10x_it.o(.bss)
    uart_rx_buffer                           0x200000cc   Data           9  stm32f10x_it.o(.bss)
    STACK                                    0x200000d8   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    main                                     0x08000145   Thumb Code   296  main.o(.text)
    TimingDelay_Decrement                    0x080002b5   Thumb Code     2  delay.o(.text)
    Delay_ns                                 0x080002b7   Thumb Code    12  delay.o(.text)
    Delay_1us                                0x080002c3   Thumb Code    26  delay.o(.text)
    Delay_2us                                0x080002dd   Thumb Code    26  delay.o(.text)
    Delay_10us                               0x080002f7   Thumb Code    36  delay.o(.text)
    Delay_250us                              0x0800031b   Thumb Code    36  delay.o(.text)
    Delay_882us                              0x0800033f   Thumb Code    26  delay.o(.text)
    Delay_1ms                                0x08000359   Thumb Code    36  delay.o(.text)
    Delay_5ms                                0x0800037d   Thumb Code    38  delay.o(.text)
    Delay_50ms                               0x080003a3   Thumb Code    40  delay.o(.text)
    Delay                                    0x080003cb   Thumb Code    18  delay.o(.text)
    USART1_Init                              0x080003dd   Thumb Code   154  peripheralinit.o(.text)
    PeripheralInit                           0x08000477   Thumb Code    20  peripheralinit.o(.text)
    MAX262_GPIO_Init                         0x0800048b   Thumb Code    68  peripheralinit.o(.text)
    USART1_SendString                        0x080004cf   Thumb Code    38  peripheralinit.o(.text)
    Qn                                       0x08000501   Thumb Code    40  max262.o(.text)
    Filter1                                  0x08000529   Thumb Code   348  max262.o(.text)
    Filter2                                  0x08000685   Thumb Code   372  max262.o(.text)
    TIM2_GPIO_Init                           0x08000805   Thumb Code    38  pwmoutput.o(.text)
    TIM2_Mode_Init                           0x0800082b   Thumb Code   120  pwmoutput.o(.text)
    TIM2_PWMOutput_Init                      0x080008a3   Thumb Code    12  pwmoutput.o(.text)
    Key_Init                                 0x080008c1   Thumb Code    38  key.o(.text)
    Key_Scan                                 0x080008e7   Thumb Code   164  key.o(.text)
    Wr_CodeData                              0x08000991   Thumb Code   166  charlcd.o(.text)
    WrCLcdD                                  0x08000a37   Thumb Code    38  charlcd.o(.text)
    WrCLcdC                                  0x08000a5d   Thumb Code    38  charlcd.o(.text)
    CG_Write                                 0x08000a83   Thumb Code    30  charlcd.o(.text)
    GPIO_LCM_Configuration                   0x08000aa1   Thumb Code    64  charlcd.o(.text)
    LCM_Init                                 0x08000ae1   Thumb Code    60  charlcd.o(.text)
    WriteString                              0x08000b1d   Thumb Code    52  charlcd.o(.text)
    WrCLcd_char_num                          0x08000b51   Thumb Code   174  charlcd.o(.text)
    WrCLcd_int_num                           0x08000bff   Thumb Code   226  charlcd.o(.text)
    WrCLcd_long_num                          0x08000ce1   Thumb Code   454  charlcd.o(.text)
    Wr_In1                                   0x08000ea7   Thumb Code    36  charlcd.o(.text)
    Wr_In2                                   0x08000ecb   Thumb Code    36  charlcd.o(.text)
    CL_Enter                                 0x08000eef   Thumb Code    44  charlcd.o(.text)
    CR_Enter                                 0x08000f1b   Thumb Code    42  charlcd.o(.text)
    L_Enter                                  0x08000f45   Thumb Code    42  charlcd.o(.text)
    R_Enter                                  0x08000f6f   Thumb Code    42  charlcd.o(.text)
    CGWrite                                  0x08000f99   Thumb Code    62  charlcd.o(.text)
    NMI_Handler                              0x08000fe9   Thumb Code     2  stm32f10x_it.o(.text)
    HardFault_Handler                        0x08000feb   Thumb Code     4  stm32f10x_it.o(.text)
    MemManage_Handler                        0x08000fef   Thumb Code     4  stm32f10x_it.o(.text)
    BusFault_Handler                         0x08000ff3   Thumb Code     4  stm32f10x_it.o(.text)
    UsageFault_Handler                       0x08000ff7   Thumb Code     4  stm32f10x_it.o(.text)
    SVC_Handler                              0x08000ffb   Thumb Code     2  stm32f10x_it.o(.text)
    DebugMon_Handler                         0x08000ffd   Thumb Code     2  stm32f10x_it.o(.text)
    PendSV_Handler                           0x08000fff   Thumb Code     2  stm32f10x_it.o(.text)
    SysTick_Handler                          0x08001001   Thumb Code     2  stm32f10x_it.o(.text)
    USART1_IRQHandler                        0x08001003   Thumb Code   110  stm32f10x_it.o(.text)
    NVIC_PriorityGroupConfig                 0x0800108d   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08001097   Thumb Code   100  misc.o(.text)
    NVIC_SetVectorTable                      0x080010fb   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08001109   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x0800112b   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x08001169   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08001215   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x08001229   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x0800133f   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x0800134f   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x08001361   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08001369   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x0800137b   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x08001383   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x08001387   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x0800138b   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x08001395   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x08001399   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x080013ab   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x080013c5   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x080013cb   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x08001455   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x08001497   Thumb Code     8  stm32f10x_gpio.o(.text)
    RCC_DeInit                               0x080014c5   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x08001505   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x0800154b   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08001583   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x080015bb   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x080015cf   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x080015d5   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x080015ed   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x080015f3   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08001605   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x0800160f   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x08001621   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x08001633   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x08001647   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x08001661   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x08001669   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x0800167b   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x080016ad   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x080016b3   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x080016bf   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x080016c7   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x08001787   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x080017a1   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x080017bb   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x080017d5   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x080017ef   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x08001809   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08001811   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x08001817   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x0800181d   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x0800182b   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x0800183f   Thumb Code     6  stm32f10x_rcc.o(.text)
    TIM_DeInit                               0x08001869   Thumb Code   424  stm32f10x_tim.o(.text)
    TIM_TimeBaseInit                         0x08001a11   Thumb Code   122  stm32f10x_tim.o(.text)
    TIM_OC1Init                              0x08001a8b   Thumb Code   132  stm32f10x_tim.o(.text)
    TIM_OC2Init                              0x08001b0f   Thumb Code   154  stm32f10x_tim.o(.text)
    TIM_OC3Init                              0x08001ba9   Thumb Code   150  stm32f10x_tim.o(.text)
    TIM_OC4Init                              0x08001c3f   Thumb Code   182  stm32f10x_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08001cf5   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08001d91   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08001e1d   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_SetIC1Prescaler                      0x08001eb9   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ICInit                               0x08001f37   Thumb Code   150  stm32f10x_tim.o(.text)
    TIM_PWMIConfig                           0x08001fcd   Thumb Code   124  stm32f10x_tim.o(.text)
    TIM_BDTRConfig                           0x08002049   Thumb Code    32  stm32f10x_tim.o(.text)
    TIM_TimeBaseStructInit                   0x08002069   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OCStructInit                         0x0800207b   Thumb Code    20  stm32f10x_tim.o(.text)
    TIM_ICStructInit                         0x0800208f   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_BDTRStructInit                       0x080020a1   Thumb Code    40  stm32f10x_tim.o(.text)
    TIM_Cmd                                  0x080020c9   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x080020e1   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_ITConfig                             0x080020ff   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_GenerateEvent                        0x08002111   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_DMAConfig                            0x08002115   Thumb Code    10  stm32f10x_tim.o(.text)
    TIM_DMACmd                               0x0800211f   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_InternalClockConfig                  0x08002131   Thumb Code    12  stm32f10x_tim.o(.text)
    TIM_SelectInputTrigger                   0x0800213d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x0800214f   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_TIxExternalClockConfig               0x08002167   Thumb Code    62  stm32f10x_tim.o(.text)
    TIM_ETRConfig                            0x080021a5   Thumb Code    28  stm32f10x_tim.o(.text)
    TIM_ETRClockMode1Config                  0x080021c1   Thumb Code    54  stm32f10x_tim.o(.text)
    TIM_ETRClockMode2Config                  0x080021f7   Thumb Code    32  stm32f10x_tim.o(.text)
    TIM_PrescalerConfig                      0x08002217   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_CounterModeConfig                    0x0800221d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x0800222f   Thumb Code    66  stm32f10x_tim.o(.text)
    TIM_ForcedOC1Config                      0x08002271   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ForcedOC2Config                      0x08002283   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ForcedOC3Config                      0x0800229d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ForcedOC4Config                      0x080022af   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ARRPreloadConfig                     0x080022c9   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectCOM                            0x080022e1   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectCCDMA                          0x080022f9   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_CCPreloadControl                     0x08002311   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_OC1PreloadConfig                     0x08002329   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2PreloadConfig                     0x0800233b   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3PreloadConfig                     0x08002355   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC4PreloadConfig                     0x08002367   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC1FastConfig                        0x08002381   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2FastConfig                        0x08002393   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3FastConfig                        0x080023ad   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC4FastConfig                        0x080023bf   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ClearOC1Ref                          0x080023d9   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearOC2Ref                          0x080023eb   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_ClearOC3Ref                          0x08002403   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearOC4Ref                          0x08002415   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_OC1PolarityConfig                    0x0800242d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x0800243f   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2PolarityConfig                    0x08002451   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x0800246b   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3PolarityConfig                    0x08002485   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x0800249f   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC4PolarityConfig                    0x080024b9   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_CCxCmd                               0x080024d3   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_CCxNCmd                              0x080024f1   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_SelectOCxM                           0x0800250f   Thumb Code    82  stm32f10x_tim.o(.text)
    TIM_UpdateDisableConfig                  0x08002561   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_UpdateRequestConfig                  0x08002579   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectHallSensor                     0x08002591   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectOnePulseMode                   0x080025a9   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectOutputTrigger                  0x080025bb   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectSlaveMode                      0x080025cd   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x080025df   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SetCounter                           0x080025f1   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetAutoreload                        0x080025f5   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare1                          0x080025f9   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare2                          0x080025fd   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare3                          0x08002601   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare4                          0x08002605   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_SetClockDivision                     0x0800260b   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_GetCapture1                          0x0800261d   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture2                          0x08002623   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture3                          0x08002629   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture4                          0x0800262f   Thumb Code     8  stm32f10x_tim.o(.text)
    TIM_GetCounter                           0x08002637   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetPrescaler                         0x0800263d   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetFlagStatus                        0x08002643   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearFlag                            0x08002655   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetITStatus                          0x0800265b   Thumb Code    34  stm32f10x_tim.o(.text)
    TIM_ClearITPendingBit                    0x0800267d   Thumb Code     6  stm32f10x_tim.o(.text)
    USART_DeInit                             0x08002685   Thumb Code   134  stm32f10x_usart.o(.text)
    USART_Init                               0x0800270b   Thumb Code   210  stm32f10x_usart.o(.text)
    USART_StructInit                         0x080027dd   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ClockInit                          0x080027f5   Thumb Code    34  stm32f10x_usart.o(.text)
    USART_ClockStructInit                    0x08002817   Thumb Code    12  stm32f10x_usart.o(.text)
    USART_Cmd                                0x08002823   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ITConfig                           0x0800283b   Thumb Code    74  stm32f10x_usart.o(.text)
    USART_DMACmd                             0x08002885   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_SetAddress                         0x08002897   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_WakeUpConfig                       0x080028a9   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x080028bb   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x080028d3   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_LINCmd                             0x080028e5   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SendData                           0x080028fd   Thumb Code     8  stm32f10x_usart.o(.text)
    USART_ReceiveData                        0x08002905   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SendBreak                          0x0800290f   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SetGuardTime                       0x08002919   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SetPrescaler                       0x08002929   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SmartCardCmd                       0x08002939   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08002951   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_HalfDuplexCmd                      0x08002969   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_OverSampling8Cmd                   0x08002981   Thumb Code    22  stm32f10x_usart.o(.text)
    USART_OneBitMethodCmd                    0x08002997   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_IrDAConfig                         0x080029af   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_IrDACmd                            0x080029c1   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_GetFlagStatus                      0x080029d9   Thumb Code    26  stm32f10x_usart.o(.text)
    USART_ClearFlag                          0x080029f3   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_GetITStatus                        0x08002a05   Thumb Code    84  stm32f10x_usart.o(.text)
    USART_ClearITPendingBit                  0x08002a59   Thumb Code    52  stm32f10x_usart.o(.text)
    SystemInit                               0x08002b6b   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x08002bb9   Thumb Code   142  system_stm32f10x.o(.text)
    Reset_Handler                            0x08002c6d   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08002c87   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_ldivmod                          0x08002c91   Thumb Code    98  ldiv.o(.text)
    __aeabi_fadd                             0x08002cf3   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x08002d97   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x08002d9d   Thumb Code     6  fadd.o(.text)
    __aeabi_fdiv                             0x08002da3   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x08002e1f   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08002f61   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08002f67   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08002f6d   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08003051   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x0800312f   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x08003151   Thumb Code    26  dfltui.o(.text)
    __aeabi_f2uiz                            0x0800316b   Thumb Code    40  ffixui.o(.text)
    __aeabi_d2uiz                            0x08003193   Thumb Code    50  dfixui.o(.text)
    __aeabi_uldivmod                         0x080031c5   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08003227   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08003227   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08003245   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08003245   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08003265   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08003265   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08003289   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08003289   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800329b   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x080032f7   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08003315   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x080033b1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080033b1   Thumb Code     0  init.o(.text)
    __scatterload_copy                       0x080033d5   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080033e3   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080033e5   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x080033f4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003414   Number         0  anon$$obj.o(Region$$Table)
    Fn                                       0x20000000   Data           1  main.o(.data)
    uart_fre1                                0x20000004   Data           4  main.o(.data)
    uart_q                                   0x20000008   Data           4  main.o(.data)
    uart_mode                                0x2000000c   Data           1  main.o(.data)
    uart_data_ready                          0x2000000d   Data           1  main.o(.data)
    fclk                                     0x20000010   Data           8  main.o(.data)
    counterf                                 0x20000018   Data           4  pwmoutput.o(.data)
    divf                                     0x2000001c   Data           2  pwmoutput.o(.data)
    radio_num                                0x2000001e   Data           2  pwmoutput.o(.data)
    CGTab                                    0x20000020   Data          64  charlcd.o(.data)
    tab1                                     0x20000060   Data          16  charlcd.o(.data)
    tab2                                     0x20000070   Data          15  charlcd.o(.data)
    tabdy                                    0x2000007f   Data          32  charlcd.o(.data)
    lcdbuff                                  0x2000009f   Data           1  charlcd.o(.data)
    lcdbuff_1                                0x200000a0   Data           1  charlcd.o(.data)
    SystemCoreClock                          0x200000b8   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x200000bc   Data          16  system_stm32f10x.o(.data)
    __initial_sp                             0x200004d8   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000034e0, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003414, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          289    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO          294  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO          317    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO          320    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO          322    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO          324    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO          325    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO          327    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO          329    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO          318    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000170   Code   RO            1    .text               main.o
    0x080002b4   0x080002b4   0x00000128   Code   RO           79    .text               delay.o
    0x080003dc   0x080003dc   0x00000124   Code   RO           94    .text               peripheralinit.o
    0x08000500   0x08000500   0x00000304   Code   RO          106    .text               max262.o
    0x08000804   0x08000804   0x000000bc   Code   RO          123    .text               pwmoutput.o
    0x080008c0   0x080008c0   0x000000d0   Code   RO          138    .text               key.o
    0x08000990   0x08000990   0x00000658   Code   RO          150    .text               charlcd.o
    0x08000fe8   0x08000fe8   0x000000a4   Code   RO          165    .text               stm32f10x_it.o
    0x0800108c   0x0800108c   0x000000dc   Code   RO          184    .text               misc.o
    0x08001168   0x08001168   0x0000035c   Code   RO          196    .text               stm32f10x_gpio.o
    0x080014c4   0x080014c4   0x000003a4   Code   RO          208    .text               stm32f10x_rcc.o
    0x08001868   0x08001868   0x00000e1a   Code   RO          222    .text               stm32f10x_tim.o
    0x08002682   0x08002682   0x00000002   PAD
    0x08002684   0x08002684   0x00000408   Code   RO          246    .text               stm32f10x_usart.o
    0x08002a8c   0x08002a8c   0x000001e0   Code   RO          269    .text               system_stm32f10x.o
    0x08002c6c   0x08002c6c   0x00000024   Code   RO          290    .text               startup_stm32f10x_hd.o
    0x08002c90   0x08002c90   0x00000062   Code   RO          297    .text               mc_w.l(ldiv.o)
    0x08002cf2   0x08002cf2   0x000000b0   Code   RO          299    .text               mf_w.l(fadd.o)
    0x08002da2   0x08002da2   0x0000007c   Code   RO          301    .text               mf_w.l(fdiv.o)
    0x08002e1e   0x08002e1e   0x0000014e   Code   RO          303    .text               mf_w.l(dadd.o)
    0x08002f6c   0x08002f6c   0x000000e4   Code   RO          305    .text               mf_w.l(dmul.o)
    0x08003050   0x08003050   0x000000de   Code   RO          307    .text               mf_w.l(ddiv.o)
    0x0800312e   0x0800312e   0x00000022   Code   RO          309    .text               mf_w.l(dflti.o)
    0x08003150   0x08003150   0x0000001a   Code   RO          311    .text               mf_w.l(dfltui.o)
    0x0800316a   0x0800316a   0x00000028   Code   RO          313    .text               mf_w.l(ffixui.o)
    0x08003192   0x08003192   0x00000032   Code   RO          315    .text               mf_w.l(dfixui.o)
    0x080031c4   0x080031c4   0x00000062   Code   RO          331    .text               mc_w.l(uldiv.o)
    0x08003226   0x08003226   0x0000001e   Code   RO          333    .text               mc_w.l(llshl.o)
    0x08003244   0x08003244   0x00000020   Code   RO          335    .text               mc_w.l(llushr.o)
    0x08003264   0x08003264   0x00000024   Code   RO          337    .text               mc_w.l(llsshr.o)
    0x08003288   0x08003288   0x00000000   Code   RO          339    .text               mc_w.l(iusefp.o)
    0x08003288   0x08003288   0x0000006e   Code   RO          340    .text               mf_w.l(fepilogue.o)
    0x080032f6   0x080032f6   0x000000ba   Code   RO          342    .text               mf_w.l(depilogue.o)
    0x080033b0   0x080033b0   0x00000024   Code   RO          344    .text               mc_w.l(init.o)
    0x080033d4   0x080033d4   0x0000000e   Code   RO          348    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080033e2   0x080033e2   0x00000002   Code   RO          349    i.__scatterload_null  mc_w.l(handlers.o)
    0x080033e4   0x080033e4   0x0000000e   Code   RO          350    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080033f2   0x080033f2   0x00000002   PAD
    0x080033f4   0x080033f4   0x00000020   Data   RO          346    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003414, Size: 0x000004d8, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003414   0x00000018   Data   RW            2    .data               main.o
    0x20000018   0x0800342c   0x00000008   Data   RW          124    .data               pwmoutput.o
    0x20000020   0x08003434   0x00000081   Data   RW          151    .data               charlcd.o
    0x200000a1   0x080034b5   0x00000001   Data   RW          167    .data               stm32f10x_it.o
    0x200000a2   0x080034b6   0x00000014   Data   RW          209    .data               stm32f10x_rcc.o
    0x200000b6   0x080034ca   0x00000002   PAD
    0x200000b8   0x080034cc   0x00000014   Data   RW          270    .data               system_stm32f10x.o
    0x200000cc        -       0x00000009   Zero   RW          166    .bss                stm32f10x_it.o
    0x200000d5   0x080034e0   0x00000003   PAD
    0x200000d8        -       0x00000400   Zero   RW          287    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1624         88          0        129          0       5208   charlcd.o
         0          0          0          0          0         32   core_cm3.o
       296          0          0          0          0       2661   delay.o
       208          6          0          0          0        784   key.o
       368         72          0         24          0     254740   main.o
       772         12          0          0          0       1429   max262.o
       220         22          0          0          0       2013   misc.o
       292         12          0          0          0       1256   peripheralinit.o
       188         18          0          8          0       1334   pwmoutput.o
        36          8        304          0       1024        900   startup_stm32f10x_hd.o
       860         38          0          0          0       5941   stm32f10x_gpio.o
       164         28          0          1          9       2553   stm32f10x_it.o
       932         36          0         20          0       9204   stm32f10x_rcc.o
      3610         88          0          0          0      23052   stm32f10x_tim.o
      1032         22          0          0          0       8668   stm32f10x_usart.o
       480         38          0         20          0       2163   system_stm32f10x.o

    ----------------------------------------------------------------------
     11084        <USER>        <GROUP>        204       1036     321938   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          2          3          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        98          0          0          0          0         84   ldiv.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        98          0          0          0          0         92   uldiv.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        50          0          0          0          0         76   dfixui.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        40          0          0          0          0         68   ffixui.o

    ----------------------------------------------------------------------
      1912         <USER>          <GROUP>          0          0       1660   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       380         16          0          0          0        448   mc_w.l
      1530          0          0          0          0       1212   mf_w.l

    ----------------------------------------------------------------------
      1912         <USER>          <GROUP>          0          0       1660   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12996        504        336        204       1036     321722   Grand Totals
     12996        504        336        204       1036     321722   ELF Image Totals
     12996        504        336        204          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13332 (  13.02kB)
    Total RW  Size (RW Data + ZI Data)              1240 (   1.21kB)
    Total ROM Size (Code + RO Data + RW Data)      13536 (  13.22kB)

==============================================================================

