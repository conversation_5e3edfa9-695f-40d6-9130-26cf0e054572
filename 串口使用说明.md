# MAX262串口控制使用说明

## 功能描述
本程序已经添加了串口1功能，可以通过串口接收三个参数来控制MAX262滤波器：
1. **fre1** - 频率值（Hz）
2. **q值** - 品质因数（浮点数）
3. **模式** - Filter1函数的模式参数（0或1）

## 串口配置
- **波特率**: 115200
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无
- **硬件流控**: 无
- **串口引脚**: 
  - TX: PA9
  - RX: PA10

## 使用方法

### 1. 连接串口
将串口调试工具（如串口助手、PuTTY等）连接到STM32的串口1（PA9/PA10）。

### 2. 发送数据格式
按照以下顺序发送三个数据，每个数据后面按回车键（\r\n）：

```
1000        # 第一个数据：频率值（Hz）
0.842       # 第二个数据：Q值（浮点数）
1           # 第三个数据：模式（0或1）
```

### 3. 数据范围
- **频率值**: 1 ~ 100000 Hz
- **Q值**: 浮点数，建议范围 0.1 ~ 10.0
- **模式**: 0 或 1

### 4. 系统响应
- 系统启动后会发送欢迎信息和使用说明
- 接收到完整的三个参数后，会发送确认信息
- 系统会自动应用新的参数到Filter1函数

## 示例操作

1. 打开串口调试工具，设置波特率115200
2. 连接到STM32
3. 系统会显示：
   ```
   MAX262 Control System Ready
   Please send data in format:
   1. Frequency (Hz)
   2. Q value (float)
   3. Mode (0 or 1)
   Example: 1000<Enter> 0.842<Enter> 1<Enter>
   ```
4. 依次发送：
   ```
   2000<回车>
   1.5<回车>
   0<回车>
   ```
5. 系统会响应：`Data received: Freq=OK`

## 注意事项

1. **数据顺序**: 必须按照 频率→Q值→模式 的顺序发送
2. **数据格式**: 每个数据后必须发送回车符
3. **频率限制**: 系统会自动限制频率在1-100000Hz范围内
4. **实时更新**: 接收到完整的三个参数后，系统会立即更新Filter1的参数

## 技术实现

- 使用USART1接收中断处理数据
- 状态机方式解析三个参数
- 自动字符串转数值转换
- 实时参数更新到Filter1函数

## 故障排除

1. **无响应**: 检查串口连接和波特率设置
2. **数据错误**: 确保按正确顺序发送数据
3. **参数无效**: 检查数据格式和范围
