# MAX262串口控制使用说明

## 功能描述
本程序已经添加了串口1功能，可以通过串口接收二进制数据包来控制MAX262滤波器：
1. **Q值** - 品质因数（4字节float）
2. **频率值** - 频率值（4字节32位整数，Hz）
3. **模式** - Filter1函数的模式参数（1字节，0或1）

## 串口配置
- **波特率**: 115200
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无
- **硬件流控**: 无
- **串口引脚**: 
  - TX: PA9
  - RX: PA10

## 使用方法

### 1. 连接串口
将另一个单片机的串口连接到STM32的串口1（PA9/PA10）。

### 2. 数据包格式
发送9字节的二进制数据包，格式如下：

```
字节 0-3:  Q值 (float, 4字节, 小端序)
字节 4-7:  频率值 (uint32_t, 4字节, 小端序, Hz)
字节 8:    模式 (uint8_t, 1字节, 0或1)
```

### 3. 数据范围
- **Q值**: 浮点数，建议范围 0.1 ~ 10.0
- **频率值**: 1 ~ 100000 Hz
- **模式**: 0 或 1

### 4. 系统响应
- 系统启动后会发送欢迎信息和数据格式说明
- 接收到完整的9字节数据包后，会发送确认信息
- 系统会自动应用新的参数到Filter1函数

## 示例操作

1. 连接另一个单片机到STM32的串口1
2. STM32系统启动后会显示：
   ```
   MAX262 Control System Ready
   Waiting for 9-byte binary data packet:
   Bytes 0-3: Q value (float)
   Bytes 4-7: Frequency (32-bit)
   Byte 8: Mode (1 byte)
   Total: 9 bytes per packet
   ```
3. 另一个单片机发送9字节二进制数据包
4. STM32会响应：`Binary packet received: Q/Freq/Mode updated`

## 发送端代码示例（另一个单片机）

```c
// 发送数据包的示例代码
typedef struct {
    float q_value;      // Q值 (4字节)
    uint32_t frequency; // 频率 (4字节)
    uint8_t mode;       // 模式 (1字节)
} __attribute__((packed)) DataPacket;

void SendDataPacket(float q, uint32_t freq, uint8_t mode)
{
    DataPacket packet;
    packet.q_value = q;
    packet.frequency = freq;
    packet.mode = mode;

    // 发送9字节数据包
    UART_SendData((uint8_t*)&packet, sizeof(packet));
}

// 使用示例
SendDataPacket(1.5f, 2000, 0);  // Q=1.5, 频率=2000Hz, 模式=0
```

## 注意事项

1. **数据包大小**: 必须发送完整的9字节数据包
2. **字节序**: 使用小端序（Little Endian）格式
3. **频率限制**: 系统会自动限制频率在1-100000Hz范围内
4. **实时更新**: 接收到完整的9字节数据包后，系统会立即更新Filter1的参数
5. **数据对齐**: 建议使用 `__attribute__((packed))` 确保结构体无填充

## 技术实现

- 使用USART1接收中断处理二进制数据
- 直接内存映射方式解析float和uint32_t数据
- 无需字符串转换，提高处理效率
- 实时参数更新到Filter1函数

## 故障排除

1. **无响应**: 检查串口连接和波特率设置
2. **数据错误**: 确保发送完整的9字节数据包
3. **参数异常**: 检查字节序和数据类型对齐
4. **浮点数错误**: 确认发送端和接收端使用相同的浮点数格式（IEEE 754）
