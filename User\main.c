//-----------------------------------------------------------------
// ��������: 
//		��Keil uVision4����ƽ̨�»���STM32�ĳ���ģ��
// ��    ��: ���ǵ���
// ��ʼ����: 2019-05-20
// �������: 2014-05-24
// �޸�����:
// ��    ��: V1.0
//   - V1.0: ����ģ��
// ���Թ���: ����STM32���İ�塢LZE_ST LINK2��MAX262ģ��
// �� �� ˵ ��:
//    MAX262ģ��     STM32���İ�
// 				WR    <--    PE12
// 				LE    <--    PE11
// 				D0    <--    PB8
// 				D1    <--    PB9
// 				A0    <--    PB10
// 				A1    <--    PB11
// 				A2    <--    PB12
// 				A3    <--    PB13
// 				GND   <-->   GND

// 				CLK�˿�<--   PA0

// ��������˵��:
// 		PWM���ṩʱ���ź�ʱ����Ҫ�ð���������������/��ֹƵ�ʣ�
// 		K1������ѡ���޸ĵ�λ�ã�
// 		K2����������/��ֹƵ�ʵļӣ�
//		K3����������/��ֹƵ�ʵļ���
//		�������ú�֮��K4��ȷ����
//		�������õ�Ƶ�ʼ����ʱ��Ƶ�ʣ���������װ��ֵ��Ԥ��Ƶֵ�����PWM����
//		����Ԥ��Ƶֵ����װ��ֵ������������˵ó���ʱ��Ƶ�ʴ���һ������
//-----------------------------------------------------------------
 
//-----------------------------------------------------------------
// ͷ�ļ����� 
//-----------------------------------------------------------------
#include <stm32f10x.h>
#include "delay.h"
#include "PeripheralInit.h"
#include "MAX262.h"
#include "CharLCD.h"
#include "key.h"
#include "PWMOutput.h"
#include "stdio.h"
#include "stm32f10x_it.h"

extern u32 counterf;             // ��װ��ֵ
extern u16 divf;                 // Ԥ��Ƶ��
extern u16 radio_num;
u8 Fn;

// 串口接收相关变量
s32 uart_fre1 = 10000;          // 串口接收的频率值，默认1000Hz
float uart_q = 0.707;           // 串口接收的Q值，默认0.842
u8 uart_mode = 0;               // 串口接收的模式值，默认1
u8 uart_data_ready = 0;         // 数据接收完成标志
	double fclk;	
//-----------------------------------------------------------------
// ������
//-----------------------------------------------------------------
int main(void)
{
	s32 fre1;              // fre0Ϊ��׼��ֵ, fre1Ϊ���ĺ��ֵ
						  // CLKʱ��, ��λΪHz

	fre1 = 10000;                          // ��ֹƵ��, ��λΪHz
	PeripheralInit();                     // �����ʼ��
	MAX262_GPIO_Init();                  // IO�ڳ�ʼ������
// 	Filter1(0, 3);                       // (mode)���Ʒ�ʽmode, ����Ƶ��f, Ʒ������q
// 	Filter2(0, 3);                       // (mode)���Ʒ�ʽmode, ����Ƶ��f, Ʒ������q
// 	Delay_1ms(100);
// 	GPIOB->ODR =0;

//	// 发送启动信息
//	Delay_1ms(100);  // 等待串口稳定


	while(1)
	{
			// 检查是否有新的串口数据
			if(uart_data_ready)
			{
				fre1 = uart_fre1;  // 使用串口接收的频率值
				uart_data_ready = 0; // 清除数据就绪标志

				// 发送确认信息

			}

			if(fre1 > 100000)  fre1=100000;     // ��ֹƵ�����Ϊ100000Hz
			else if(fre1 == 0) fre1=1;          // ����Ƶ����СΪ1Hz
			//if (fre0!=fre1)                     // �жϽ�ֹƵ���Ƿ��޸�
			//{
				if(fre1 <= 28000)                 // �ж�Ƶ�ʴ���28k, ������ѡ��40.48, ����Ϊ139.8
				{
					fclk = (double)(fre1 * NF1);                    // ���CLKʱ��, оƬ�ֲ�p11
					Fn = 63;
				}
				else 
				{
					fclk =(double)(fre1 * NF2);                    // ���CLKʱ��, оƬ�ֲ�p11
					Fn = 0;					
				}
				divf = ((72000000/fclk)/65535)+1;    // ���Ԥ��Ƶ��
				counterf = ((720000000/fclk)/divf);  // ��װ��ֵ
				if(counterf%10>=5)                   // ��װ��ֵ��������
				{
					counterf = (counterf/10)+1;
				}
				else
				{
					counterf = counterf/10;
				}
				radio_num = counterf/2;            // ���ռ�ձ�
				TIM2_PWMOutput_Init();             // ���²���PWM��
				Filter1(uart_mode, uart_q);       // 使用串口接收的模式和Q值
								Filter2(uart_mode, uart_q);       // 使用串口接收的模式和Q值

				Delay_1ms(50);		   
//				GPIOB->ODR =0;		
	}			
}


//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
