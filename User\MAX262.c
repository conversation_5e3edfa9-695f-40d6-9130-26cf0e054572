//-----------------------------------------------------------------
// ��������: 
//		MAX262��������
// ��    ��: ���ǵ���
// ��ʼ����: 2018-05-24
// �������: 2018-05-24
// �޸�����:
// ��    ��: V1.0
//   - V1.0: ����ģ��
// ���Թ���: ����STM32+FPGA����ϵͳ��ƿ����塢LZE_ST LINK2��MAX262ģ��
// ˵    ��:
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// ͷ�ļ�����
//-----------------------------------------------------------------
#include <stm32f10x.h>
#include "MAX262.h"
#include "delay.h"
#include <math.h>

extern int Fn;
//-----------------------------------------------------------------
// ���ܳ�����
//-----------------------------------------------------------------


//-----------------------------------------------------------------
// u8 Qn(float q)                     
//-----------------------------------------------------------------
// ��������: ����Q�Ŀ�����N
// ��ڲ���: Q
// �� �� ֵ: ������N
// ȫ�ֱ���: ��
// ע������: ��
//-----------------------------------------------------------------
u8 Qn(float q)                     //Ʒ�������ؼ���
{
    u8 temp;
    temp = 128-(64/q);             //���忴оƬ�ֲ����й�ʽ
    return temp; 
}

//-----------------------------------------------------------------
// void Filter1(uint8_t mode, float f, float q)
//-----------------------------------------------------------------
// ��������: �˲���2��ģʽ��Ƶ���Լ�Qֵ������
// ��ڲ���: ģʽ mode, ��ֹ/���� Ƶ�� f, Qֵ q
// �� �� ֵ: ��
// ȫ�ֱ���: ��
// ע������: ��
//-----------------------------------------------------------------
void Filter1(u8 mode, float q)
{
	uint8_t i;
	uint8_t a = 0x03;
	uint8_t  sq;
	extern u8 Fn;  // 使用全局变量Fn（由main函数中根据频率计算得出）
	i = sq = 0;
	sq = Qn(q);                     // 计算Q对应的控制字N
	LE_H;                           // ʹ�ܶ�����
	Delay_ns(200);
	WR_H;                           // д�˿�����
	Delay_ns(200);
	
	// ����ɼ�оƬ�ֲ� P15 Table4	
	
	GPIOB->BRR = 0x3f00;	          // д��ģʽ�ĵ�ַ
	Delay_ns(200);
	WR_L;                           //д�˿�����
	Delay_ns(200);
// 	GPIOB->BRR = 0x0300;
	GPIOB->ODR |= ((uint16_t)(mode & 0x03) << 8);    // ��ģʽ�������͸�D1, D0
	Delay_ns(200);
	WR_H;                           // д�˿�����
	Delay_ns(200);
	
	for(i = 0; i < 3; i++)
	{
		GPIOB -> BRR = 0x3f00;	          // �ֽ���ַ������λ��0
		GPIOB -> ODR |= (uint16_t)(i+1)<<10;          // д���ַ
		Delay_ns(200);
		WR_L;                             // д������
		Delay_ns(200);
// 		GPIOB->BRR = 0x0300;            //����λ��0
		GPIOB->ODR |= ((uint16_t)(Fn & a) << (8-2*i));		  // ��f�Ŀ�����Nд��
		Delay_ns(200);
		WR_H;                             // д������
		a = a << 2;                       // a����2λ		
	}
	
	a = 0x03;
	
	for(i = 0; i < 4; i++)
	{
		GPIOB -> BRR = 0x3f00;	          // �ֽ���ַ������λ��0
		GPIOB -> ODR |= (uint16_t)(i+4)<<10;          // д���ַ
		Delay_ns(200);
		WR_L;                             // д������
		Delay_ns(200);
// 		GPIOB->BRR = 0x0300;              //����λ��0
		GPIOB->ODR |= ((uint16_t)(sq & a) << (8-2*i));		  // ��Q�Ŀ�����Nд��
		Delay_ns(200);                   
		WR_H;                             // д������
		a = a << 2;                       // a����2λ		
	}
}

//-----------------------------------------------------------------
// void Filter2(uint8_t mode, float f, float q)
//-----------------------------------------------------------------
// ��������: �˲���2��ģʽ��Ƶ���Լ�Qֵ������
// ��ڲ���: ģʽ mode, ��ֹ/���� Ƶ�� f, Qֵ q
// �� �� ֵ: ��
// ȫ�ֱ���: ��
// ע������: ��
//-----------------------------------------------------------------
void Filter2(u8 mode, float q)
{
	uint8_t i;
	uint8_t a = 0x03;
	uint8_t sq;
	extern u8 Fn;  // 使用全局变量Fn（由main函数中根据频率计算得出）
	i = sq = 0;
	sq = Qn(q);                     // 计算Q对应的控制字N
	LE_H;                           // ʹ�ܶ�����
	Delay_ns(200);
	WR_H;                           // д�˿�����
	Delay_ns(200);
	
	GPIOB->BRR = 0x3f00;	          // д��ģʽ�ĵ�ַ
	GPIOB -> ODR |= (uint16_t)(i+8)<<10;          // д���ַ
	Delay_ns(200);
	WR_L;                           // д�˿�����
	Delay_ns(200);
// 	GPIOB->BRR = 0x0300;
	GPIOB->ODR |= ((uint16_t)(mode & 0x03) << 8);    //��ģʽ�������͸�D1, D0
	Delay_ns(200);
	WR_H;                           // д�˿�����
	Delay_ns(200);
	
	for(i = 0; i < 3; i++)
	{
		GPIOB -> BRR = 0x3f00;	          // �ֽ���ַ������λ��0
		GPIOB -> ODR |= (uint16_t)(i+9)<<10;          // д���ַ
		Delay_ns(200);
		WR_L;                             // д������
		Delay_ns(200);
// 		GPIOB->BRR = 0x0300;            //����λ��0
		GPIOB->ODR |= ((uint16_t)(Fn & a) << (8-2*i));		  // ��f�Ŀ�����Nд��
		Delay_ns(200);                   
		WR_H;                             // д������
		a = a << 2;                       // a����2λ		
	}
	
	a = 0x03;
	
	for(i = 0; i < 4; i++)
	{
		GPIOB -> BRR = 0x3f00;	          // �ֽ���ַ������λ��0
		GPIOB -> ODR |= (uint16_t)(i+12)<<10;          // д���ַ
		Delay_ns(200);
		WR_L;                             // д������
		Delay_ns(200);
		GPIOB->ODR |= ((uint16_t)(sq & a) << (8-2*i));		  // ��Q�Ŀ�����Nд��
		Delay_ns(200);                   
		WR_H;                             // д������
		a = a << 2;                       // a����2λ		
	}
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
