//-----------------------------------------------------------------
// ��������: 
//		��Keil uVision4����ƽ̨�»���STM32�ĳ���ģ�壬�����ʼ���Լ�����
// ��    ��: ���ǵ���
// ��ʼ����: 2014-04-24
// �������: 2014-04-24
// �޸�����:
// ��    ��: V1.0
//   - V1.0: ����ģ��
// ���Թ���: ����STM32+FPGA����ϵͳ��ƿ����塢LZE_ST LINK2��2.8��Һ����
// ˵    ��:
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// ͷ�ļ�����
//-----------------------------------------------------------------
#include <stm32f10x.h>
#include "stm32f10x_usart.h"
#include "stm32f10x_gpio.h"
#include "stm32f10x_rcc.h"
#include "misc.h"
#include "PeripheralInit.h"
#include "PWMOutput.h"
#include "key.h"
#include "CharLCD.h"
//-----------------------------------------------------------------
// ���ܳ�����
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// void PeripheralInit(void)
//-----------------------------------------------------------------
// ��������: �����ʼ������
// ��ڲ���: �� 
// �� �� ֵ: ��
// ȫ�ֱ���: ��
// ע������: ��
//-----------------------------------------------------------------
void PeripheralInit(void)
{
	LCM_Init();
	Key_Init();
// 	MAX262_GPIO_Init();
	TIM2_PWMOutput_Init();
	USART1_Init();  // 初始化串口1
}

void MAX262_GPIO_Init(void)               // MAX262��������
{
	GPIO_InitTypeDef GPIO_InitStructure;

	//ʹ��IO��ʱ��
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB|RCC_APB2Periph_GPIOE, ENABLE);
	
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8  | GPIO_Pin_9          // D0, D1 
															| GPIO_Pin_10 | GPIO_Pin_11         // A0, A1
															| GPIO_Pin_12 | GPIO_Pin_13;        // A2, A3
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;                // �������
	GPIO_Init(GPIOB,&GPIO_InitStructure);

	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_14  | GPIO_Pin_15 ;        // EN, WR 
// 	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;                // �������
	GPIO_Init(GPIOB,&GPIO_InitStructure);

}

//-----------------------------------------------------------------
// void USART1_Init(void)
//-----------------------------------------------------------------
// 功能描述: 串口1初始化函数
// 入口参数: 无
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 波特率115200，8位数据位，1位停止位，无校验
//-----------------------------------------------------------------
void USART1_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	USART_InitTypeDef USART_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;

	// 使能USART1和GPIOA时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1 | RCC_APB2Periph_GPIOA, ENABLE);

	// 配置USART1 Tx (PA9)为复用推挽输出
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_Init(GPIOA, &GPIO_InitStructure);

	// 配置USART1 Rx (PA10)为浮空输入
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
	GPIO_Init(GPIOA, &GPIO_InitStructure);

	// 配置USART1参数
	USART_InitStructure.USART_BaudRate = 115200;                    // 波特率115200
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;     // 8位数据位
	USART_InitStructure.USART_StopBits = USART_StopBits_1;         // 1位停止位
	USART_InitStructure.USART_Parity = USART_Parity_No;            // 无校验
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; // 无硬件流控
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx; // 收发模式
	USART_Init(USART1, &USART_InitStructure);

	// 配置USART1接收中断
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);

	// 配置NVIC
	NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);

	// 使能USART1
	USART_Cmd(USART1, ENABLE);
}

//-----------------------------------------------------------------
// void USART1_SendString(char* str)
//-----------------------------------------------------------------
// 功能描述: 串口1发送字符串函数
// 入口参数: str - 要发送的字符串
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 用于调试输出
//-----------------------------------------------------------------
void USART1_SendString(char* str)
{
	while(*str)
	{
		while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);
		USART_SendData(USART1, *str++);
	}
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
